<template>
  <Transition name="floor-transition">
    <div v-if="shouldShow" class="fixed inset-0 z-[998] pointer-events-none select-none flex items-center justify-center">
      <!-- 简洁的楼层切换loading -->
      <div class="bg-black/60 backdrop-blur-sm rounded-lg px-6 py-4 flex items-center gap-4 text-white shadow-lg">
        <!-- Loading 动画 -->
        <div class="w-6 h-6 border-2 border-blue-400 border-t-transparent rounded-full animate-spin"></div>

        <!-- 楼层切换文本 -->
        <div class="flex items-center gap-2">
          <span class="text-sm">切换至</span>
          <span class="font-medium">{{ targetFloorName }}</span>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { useGlobalThreeStore } from '../store/globalThreeStore';

  const store = useGlobalThreeStore();

  // 计算属性
  const shouldShow = computed(() => store.floorTransitionState.isTransitioning);
  const targetFloorId = computed(() => store.floorTransitionState.targetFloorId);

  // 楼层信息
  const targetFloorName = computed(() => {
    if (!targetFloorId.value) return '';
    // 从楼层ID提取楼层名称
    const floorMap: Record<string, string> = {
      BF: '水泵房',
      '1F': '一楼',
      '2F': '二楼',
      '3F': '三楼',
      '4F': '四楼',
      '5F': '五楼',
    };
    return floorMap[targetFloorId.value] || targetFloorId.value;
  });
</script>

<style scoped>
  .floor-transition-enter-active,
  .floor-transition-leave-active {
    transition: all 0.3s ease-out;
  }

  .floor-transition-enter-from,
  .floor-transition-leave-to {
    opacity: 0;
    transform: scale(0.9);
  }

  .floor-transition-enter-to,
  .floor-transition-leave-from {
    opacity: 1;
    transform: scale(1);
  }
</style>
