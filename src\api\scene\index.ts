import { useGlobSetting } from '/@/hooks/setting';
const { modelUrl } = useGlobSetting();

// 模型路径常量定义
export const ExteriorModel = `${modelUrl}/models/waijing.glb`; // 合并后的外景模型
export const DroneDynamic = `${modelUrl}/models/drone.glb`;
export const FloorB1 = `${modelUrl}/models/bf.glb`; // 负一楼模型
export const Floor1 = `${modelUrl}/models/1f.glb`;
export const Floor2 = `${modelUrl}/models/2.glb`;
export const Floor3 = `${modelUrl}/models/3.glb`;
export const Floor4 = `${modelUrl}/models/4.glb`;
export const Floor5 = `${modelUrl}/models/5.glb`;

import { defHttp } from '/@/utils/http/axios';

interface RoomInfo {
  id: number;
  totalMan: number;
  car: number;
  alarm: number;
  device: number;
}

export interface BADataItem {
  id: number;
  name: string;
  remark: string;
  valueData: string;
  unit: string;
  type: number;
  addr: number;
  updateTime: string;
}

export interface BAAlarmItem {
  id: number;
  name: string;
  remark: string;
  alarmType: string;
  alarmTime: string;
  valueData?: string; // 添加可选的valueData字段，兼容后端返回格式
}

// 更新设备详情数据接口，匹配实际返回的数据结构
export interface DeviceDetailData {
  id: number | null; // 允许 id 为 null
  name: string;
  code: string;
  type: number; // 1-模拟量，2-设定值
  remark: string;
  dataTime: string;
  valueData: string;
  addr: number;
}

// 添加API响应类型定义
interface ApiResponse<T> {
  success: boolean;
  result: T;
  message?: string;
}

// 定义BA数据查询参数接口
export interface BADataParams {
  pageNum?: number;
  pageSize?: number;
  remark?: string;
  [key: string]: any;
}

// 定义BA告警查询参数接口
export interface BAAlarmParams {
  pageNum?: number;
  pageSize?: number;
  remark?: string;
  [key: string]: any;
}

/**
 * 场景视图专用API封装
 * 实际调用全局API但保持模块独立性
 */
import { getCameraListNew as getHaikangCameraList, _getCameraPreviewURLs } from '/@/api/safety/camera';

/**
 * 获取摄像头列表
 */
export const getCameraList = () => {
  return getHaikangCameraList();
};

/**
 * 获取摄像头预览URL
 * @param cameraIndexCode 摄像头索引码
 */
export const getCameraPreviewURLs = (cameraIndexCode: string) => {
  return _getCameraPreviewURLs(cameraIndexCode);
};

/**
 * 获取摄像头详情
 */
export const getCameraById = (id: string): Promise<Record<string, any>> => {
  return defHttp.get({
    url: `/hkCamera/${id}`,
  });
};

/**
 * 获取园区房间信息
 */
export const getRoomInfo = (): Promise<RoomInfo> => {
  return defHttp.get({
    url: '/roomInfo',
  });
};

/**
 * 更新园区房间信息
 */
export const updateRoomInfo = (data: Partial<RoomInfo>): Promise<void> => {
  return defHttp.post({
    url: '/roomInfo',
    data,
  });
};

// 定义BA数据列表响应接口
export interface BADataListResponse {
  records: BADataItem[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

/**
 * 获取BA系统数据列表
 * @param params 查询参数
 * @returns 根据参数返回数组或分页对象
 */
export const getBADataList = (params: BADataParams) => {
  // 如果有分页参数，返回分页格式；否则返回数组格式
  if (params.pageNum || params.pageSize) {
    return defHttp.post<ApiResponse<BADataListResponse>>({
      url: '/modbus/baData/list',
      data: params,
      // @ts-ignore: paramsInUrl 不在 AxiosRequestConfig 中，但用于特殊处理
      paramsInUrl: true, // 添加特殊标记，表示需要将参数拼接到URL
    });
  } else {
    return defHttp.post<BADataItem[]>({
      url: '/modbus/baData/list',
      data: params,
      // @ts-ignore: paramsInUrl 不在 AxiosRequestConfig 中，但用于特殊处理
      paramsInUrl: true, // 添加特殊标记，表示需要将参数拼接到URL
    });
  }
};

/**
 * 获取BA系统告警列表
 * @param params 查询参数
 */
export interface BAAlarmListResponse {
  records: BAAlarmItem[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

export const getBAAlarmList = (params: BAAlarmParams) => {
  return defHttp.get<ApiResponse<BAAlarmListResponse>>({
    url: '/modbus/baData/alarm',
    params,
  });
};
/** 根据实时告警查询历史告警
 * @param params 查询参数
 */
export const getBAAlarmHistory = (params: BAAlarmParams) => {
  return defHttp.get<ApiResponse<BAAlarmListResponse>>({
    url: '/baDataHistory',
    params,
  });
};

/**
 * 根据设备编码获取设备详情数据
 * @param code 设备编码
 * @returns 设备详情数据数组
 */
export const getDeviceDetailByCode = (code: string) => {
  return defHttp.get<DeviceDetailData[]>({
    url: '/baDeviceData/code',
    params: { code },
  });
};

// 折线图数据接口
export interface ChartDataPoint {
  time: string;
  value: number;
}

export interface DeviceChartData {
  id: string;
  name: string;
  remark: string;
  unit?: string;
  data: ChartDataPoint[];
}

export interface DeviceChartParams {
  id: string;
  type: number;
  startTime?: string;
  endTime?: string;
  timeRange?: '1h' | '6h' | '12h' | '24h' | '7d' | '30d' | 'custom';
}

/**
 * 获取设备折线图数据
 * @param params 查询参数
 * @returns 折线图数据
 */
export const getDeviceChartData = (params: DeviceChartParams) => {
  return defHttp.get<DeviceChartData>({
    url: '/baDeviceData/chart',
    params,
  });
};

export const ModelPaths = {
  ExteriorModel, // 使用新的合并后的外景模型
  DroneDynamic,
  FloorB1,
  Floor1,
  Floor2,
  Floor3,
  Floor4,
  Floor5,
} as const;

export type ModelPathKeys = keyof typeof ModelPaths;

export default ModelPaths;
