<template>
  <div class="w-full h-full relative" style="min-height: 400px">
    <div v-if="loading" class="absolute inset-0 flex items-center justify-center z-10 bg-white bg-opacity-80">
      <a-spin size="large" />
    </div>
    <div ref="chartRef" class="w-full h-full" :class="{ 'opacity-40 pointer-events-none': loading }" style="min-height: 400px"></div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted, nextTick } from 'vue';
  import * as echarts from 'echarts';
  import { getFloorWeight, type FloorWeightData } from '/@/api/weight';
  import { message } from 'ant-design-vue';

  const chartRef = ref<HTMLElement | null>(null);
  let chartInstance: echarts.ECharts | null = null;
  const loading = ref(false);

  const checkDomSize = (): boolean => {
    if (!chartRef.value) return false;
    const { clientWidth, clientHeight } = chartRef.value;
    return clientWidth > 0 && clientHeight > 0;
  };

  const initChart = async () => {
    if (!chartRef.value) return;

    // 等待 DOM 尺寸可用
    let attempts = 0;
    const maxAttempts = 50;

    while (!checkDomSize() && attempts < maxAttempts) {
      await new Promise((resolve) => setTimeout(resolve, 100));
      attempts++;
    }

    if (!checkDomSize()) {
      console.warn('Chart container size is still 0 after waiting');
      return;
    }

    chartInstance = echarts.init(chartRef.value);

    // 设置默认配置
    const defaultOption = {
      title: {
        text: '楼层重量',
        left: 'center',
        textStyle: {
          color: '#2c3e50',
          fontWeight: 'bold',
          fontSize: 16,
        },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        backgroundColor: 'rgba(50,50,50,0.8)',
        borderColor: '#333',
        textStyle: { color: '#fff' },
        formatter: function (params: any[]) {
          const param = params[0];
          if (param) {
            return `${param.name}<br/>${param.marker}${param.seriesName}: ${param.value} kg`;
          }
          return '';
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
        backgroundColor: '#f9f9f9',
        borderColor: '#eee',
        show: true,
      },
      xAxis: {
        type: 'category',
        data: [],
        axisLabel: {
          interval: 0,
          rotate: 45,
          formatter: function (value: string) {
            return value;
          },
        },
        axisTick: {
          alignWithLabel: true,
        },
      },
      yAxis: {
        type: 'value',
        name: '重量(kg)',
        splitLine: {
          lineStyle: {
            color: '#e0e0e0',
            type: 'dashed',
          },
        },
      },
      series: [
        {
          name: '楼层重量',
          type: 'bar',
          data: [],
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#83bff6' },
              { offset: 0.5, color: '#188df0' },
              { offset: 1, color: '#188df0' },
            ]),
          },
          emphasis: {
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#2378f7' },
                { offset: 0.7, color: '#2378f7' },
                { offset: 1, color: '#83bff6' },
              ]),
            },
          },
          animationDelay: (idx: number) => idx * 10,
        },
      ],
      animationEasing: 'elasticOut' as const,
      animationDelayUpdate: (idx: number) => idx * 5,
    };

    chartInstance.setOption(defaultOption);

    // 监听窗口变化
    window.addEventListener('resize', handleResize);
  };

  const loadData = async () => {
    try {
      loading.value = true;
      const data = await getFloorWeight();

      if (data && Array.isArray(data)) {
        const categories: string[] = [];
        const values: number[] = [];

        // 处理数据 - 使用正确的字段名
        data.forEach((item: FloorWeightData) => {
          categories.push(`${item.floors}层`);
          values.push(item.weight || 0);
        });

        // 更新图表数据
        if (chartInstance) {
          chartInstance.setOption({
            xAxis: {
              data: categories,
            },
            series: [
              {
                data: values,
              },
            ],
          });
        }

        if (categories.length === 0) {
          message.info('暂无楼层重量数据');
        }
      } else {
        message.error('获取楼层重量数据失败');
      }
    } catch (error) {
      console.error('加载楼层重量数据失败:', error);
      message.error('加载数据失败，请稍后重试');
    } finally {
      loading.value = false;
    }
  };

  const handleResize = () => {
    chartInstance?.resize();
  };

  onMounted(async () => {
    await nextTick();
    await initChart();
    loadData();
  });

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
    chartInstance?.dispose();
  });

  // 暴露刷新方法
  const refresh = () => {
    loadData();
  };

  defineExpose({
    refresh,
  });
</script>

<style scoped>
  .w-full {
    width: 100%;
  }
  .h-full {
    height: 100%;
  }
</style>
