<template>
  <div ref="textRef" class="truncate" @mouseover="checkOverflow" @mouseleave="hideTooltip">
    <a-tooltip v-if="isOverflow" :title="tooltipTitle">
      {{ tooltipTitle }}
    </a-tooltip>
    <template v-else>
      {{ tooltipTitle }}
    </template>
  </div>
</template>

<script setup>
  import { ref, nextTick, onMounted } from 'vue';

  const props = defineProps({
    tooltipTitle: {
      type: String,
      required: true,
    },
  });

  const textRef = ref(null);
  const isOverflow = ref(false);

  const checkOverflow = () => {
    nextTick(() => {
      if (textRef.value) {
        isOverflow.value = textRef.value.scrollWidth > textRef.value.clientWidth;
      }
    });
  };

  const hideTooltip = () => {
    isOverflow.value = false;
  };

  onMounted(() => {
    checkOverflow();
  });
</script>

<style scoped>
  .truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
</style>
