import { nextTick } from 'vue';

/**
 * 安全地设置表单字段值
 * 在Modal打开后等待表单完全渲染再设置值
 * @param setFieldsValue - 表单的setFieldsValue方法
 * @param values - 要设置的值
 * @param maxRetries - 最大重试次数
 * @param retryDelay - 重试延迟时间(ms)
 */
export async function safeSetFieldsValue(
  setFieldsValue: (values: any) => Promise<void>,
  values: any,
  maxRetries: number = 3,
  retryDelay: number = 100
): Promise<void> {
  let retries = 0;

  while (retries < maxRetries) {
    try {
      // 等待下一个tick，确保DOM完全渲染
      await nextTick();

      // 如果需要额外延迟，等待一小段时间
      if (retries > 0) {
        await new Promise((resolve) => setTimeout(resolve, retryDelay));
      }

      await setFieldsValue(values);
      return; // 成功设置，退出函数
    } catch (error) {
      retries++;
      console.warn(`设置表单值失败，重试 ${retries}/${maxRetries}:`, error);

      if (retries >= maxRetries) {
        throw new Error(`设置表单值失败，已重试 ${maxRetries} 次: ${error.message}`);
      }
    }
  }
}

/**
 * 创建一个安全的编辑处理函数
 * @param setFieldsValue - 表单的setFieldsValue方法
 * @param setModalVisible - 设置Modal可见性的方法
 * @param setIsEdit - 设置编辑状态的方法
 */
export function createSafeEditHandler<T = any>(
  setFieldsValue: (values: T) => Promise<void>,
  setModalVisible: (visible: boolean) => void,
  setIsEdit: (isEdit: boolean) => void
) {
  return async (record: T): Promise<void> => {
    try {
      setIsEdit(true);
      setModalVisible(true);
      await safeSetFieldsValue(setFieldsValue, record);
    } catch (error) {
      console.error('编辑处理失败:', error);
      // 可以在这里添加错误提示
      throw error;
    }
  };
}
