// 仅保留类型定义，接口实现已移动到 environment/index.ts

export interface AlarmParams {
  pageNo: number;
  pageSize: number;
}

export interface AlarmRecord {
  id: number;
  deviceName: string;
  alarmLevel: string;
  alarmDesc: string;
  alarmTime: string;
  deviceId: string;
  signalId: string;
  signalNumber: string;
  alarmFlag: number;
  eventValue: string;
  createDate: string;
  scId: string;
  serialNo: string;
  siteId: string;
}

export interface PageResult {
  records: AlarmRecord[];
  total: number;
  size: number;
  current: number;
  pages: number;
  orders: any[];
  optimizeCountSql: boolean;
  searchCount: boolean;
  maxLimit: number | null;
  countId: number | null;
}

// 注意：getAlarmList 接口已移动到 environment/index.ts 中的 getCmccAlarmList 函数
