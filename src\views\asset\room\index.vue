<template>
  <div class="p-4">
    <div class="bg-white p-4 rounded-lg shadow-sm">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-medium">房间管理</h2>
        <div class="flex gap-2">
          <a-button type="primary" @click="handleAdd">
            <template #icon>
              <PlusOutlined />
            </template>
            新增房间
          </a-button>
          <a-button @click="handleRefresh" :loading="loading">
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新
          </a-button>
        </div>
      </div>

      <!-- 搜索表单 -->
      <div class="mb-4">
        <a-form layout="inline" :model="searchForm" @finish="handleSearch" class="search-form">
          <a-form-item label="房间名称">
            <a-input v-model:value="searchForm.roomName" placeholder="请输入房间名称" allow-clear />
          </a-form-item>
          <a-form-item label="楼层">
            <a-input-number v-model:value="searchForm.floors" placeholder="请输入楼层" style="width: 120px" />
          </a-form-item>
          <a-form-item>
            <a-button type="primary" html-type="submit">查询</a-button>
            <a-button @click="handleReset" style="margin-left: 8px">重置</a-button>
          </a-form-item>
        </a-form>
      </div>

      <!-- 数据表格 -->
      <a-table
        :columns="columns"
        :data-source="tableData"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
        size="middle"
        :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleEdit(record)">编辑</a-button>
              <a-popconfirm title="确定要删除这个房间吗？" @confirm="handleDelete([record.id])">
                <a-button type="link" size="small" danger>删除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>

      <!-- 批量操作 -->
      <div v-if="selectedRowKeys.length > 0" class="mt-4">
        <a-space>
          <span>已选择 {{ selectedRowKeys.length }} 项</span>
          <a-popconfirm title="确定要删除选中的房间吗？" @confirm="handleDelete(selectedRowKeys)">
            <a-button type="primary" danger size="small">批量删除</a-button>
          </a-popconfirm>
        </a-space>
      </div>
    </div>

    <!-- 新增/编辑弹窗 -->
    <BasicModal
      v-model:open="modalVisible"
      :title="isEdit ? '编辑房间' : '新增房间'"
      @ok="handleSubmit"
      @cancel="handleCancel"
      :confirm-loading="submitLoading"
      width="600px"
      class="room-modal"
    >
      <BasicForm @register="registerRoomForm" />
    </BasicModal>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import { message } from 'ant-design-vue';
  import { PlusOutlined, ReloadOutlined } from '@ant-design/icons-vue';
  import { getRoomList, addRoom, updateRoom, deleteRoom, type RoomModel, type RoomParams } from '/@/api/asset/room';
  import { BasicModal } from '/@/components/Modal';
  import { BasicForm, useForm, FormSchema } from '/@/components/Form';
  import { safeSetFieldsValue } from '/@/utils/form';

  // 响应式数据
  const loading = ref(false);
  const submitLoading = ref(false);
  const tableData = ref<RoomModel[]>([]);
  const selectedRowKeys = ref<number[]>([]);
  const modalVisible = ref(false);
  const isEdit = ref(false);

  // 搜索表单
  const searchForm = reactive({
    roomName: '',
    floors: undefined,
  });

  // 房间表单配置
  const roomFormSchema: FormSchema[] = [
    {
      label: '',
      field: 'id',
      component: 'Input',
      show: false,
    },
    {
      label: '房间名称',
      field: 'roomName',
      required: true,
      component: 'Input',
      componentProps: {
        placeholder: '请输入房间名称',
      },
      colProps: { span: 24 },
    },
    {
      label: '楼层',
      field: 'floors',
      required: true,
      component: 'InputNumber',
      componentProps: {
        min: 1,
        placeholder: '请输入楼层',
        style: { width: '100%' },
      },
      colProps: { span: 24 },
    },
    {
      label: '备注',
      field: 'remark',
      component: 'InputTextArea',
      componentProps: {
        placeholder: '请输入备注',
        rows: 3,
      },
      colProps: { span: 24 },
    },
  ];

  // 表单配置
  const [registerRoomForm, { resetFields, setFieldsValue, validate }] = useForm({
    schemas: roomFormSchema,
    showActionButtonGroup: false,
    labelCol: {
      xs: { span: 24 },
      sm: { span: 6 },
    },
    wrapperCol: {
      xs: { span: 24 },
      sm: { span: 18 },
    },
    baseColProps: { span: 24 },
    labelWidth: 100, // 固定label宽度为100px
  });

  // 分页配置
  const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number) => `共 ${total} 条记录`,
  });

  // 表格列配置
  const columns = [
    {
      title: '序号',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      align: 'center',
    },
    {
      title: '房间名称',
      dataIndex: 'roomName',
      key: 'roomName',
      width: 200,
    },
    {
      title: '楼层',
      dataIndex: 'floors',
      key: 'floors',
      width: 100,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right',
    },
  ];

  // 构建查询参数
  const buildParams = (): RoomParams => {
    return {
      current: pagination.current,
      size: pagination.pageSize,
      roomName: searchForm.roomName || undefined,
      floors: searchForm.floors || undefined,
    };
  };

  // 加载数据
  const loadData = async () => {
    try {
      loading.value = true;
      const params = buildParams();
      const result = await getRoomList(params);

      // 直接使用result，因为HTTP函数已经返回了result字段的值
      tableData.value = result.records;
      pagination.total = result.total;
    } catch (error) {
      console.error('加载房间数据失败:', error);
      message.error('加载数据失败');
    } finally {
      loading.value = false;
    }
  };

  // 搜索
  const handleSearch = () => {
    pagination.current = 1;
    loadData();
  };

  // 重置搜索
  const handleReset = () => {
    searchForm.roomName = '';
    searchForm.floors = undefined;
    pagination.current = 1;
    loadData();
  };

  // 刷新
  const handleRefresh = () => {
    loadData();
    message.success('数据已刷新');
  };

  // 表格变化
  const handleTableChange = (pag: any) => {
    pagination.current = pag.current;
    pagination.pageSize = pag.pageSize;
    loadData();
  };

  // 选择变化
  const onSelectChange = (keys: number[]) => {
    selectedRowKeys.value = keys;
  };

  // 新增
  const handleAdd = () => {
    isEdit.value = false;
    modalVisible.value = true;
    resetFields();
  };

  // 编辑
  const handleEdit = async (record: RoomModel) => {
    isEdit.value = true;
    modalVisible.value = true;
    try {
      await safeSetFieldsValue(setFieldsValue, record);
    } catch (error) {
      console.error('设置表单值失败:', error);
      message.error('加载编辑数据失败');
    }
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await validate();
      submitLoading.value = true;

      if (isEdit.value) {
        await updateRoom(values);
      } else {
        const { id, ...addData } = values;
        await addRoom(addData);
      }

      message.success(isEdit.value ? '修改成功' : '新增成功');
      modalVisible.value = false;
      loadData();
    } catch (error) {
      console.error('提交失败:', error);
      message.error('操作失败');
    } finally {
      submitLoading.value = false;
    }
  };

  // 取消
  const handleCancel = () => {
    modalVisible.value = false;
    resetFields();
  };

  // 删除
  const handleDelete = async (ids: number[]) => {
    try {
      await deleteRoom(ids);
      message.success('删除成功');
      selectedRowKeys.value = [];
      loadData();
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };

  // 挂载时加载数据
  onMounted(() => {
    loadData();
  });
</script>

<style scoped lang="less">
  .search-form {
    :deep(.ant-form-item) {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      margin-right: 16px;

      .ant-form-item-label {
        flex: none;
        width: auto;
        margin-right: 8px;

        label {
          white-space: nowrap;
        }
      }

      .ant-form-item-control {
        flex: 1;
        min-width: 0;
      }
    }
  }

  // 房间弹窗样式优化
  :deep(.room-modal) {
    .ant-modal-body {
      padding: 24px;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      border-radius: 8px;
    }

    .ant-form {
      .ant-form-item {
        margin-bottom: 24px;
        background: rgba(255, 255, 255, 0.8);
        padding: 16px;
        border-radius: 12px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        display: flex;
        align-items: center;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
          background: rgba(255, 255, 255, 0.9);
        }

        .ant-form-item-label {
          padding-bottom: 0;
          padding-right: 12px;
          text-align: right;
          width: 100px !important;
          min-width: 100px !important;
          flex: none;

          label {
            font-weight: 600;
            color: rgba(0, 0, 0, 0.85);
            font-size: 14px;
            position: relative;
            display: inline-block;
            white-space: nowrap;
            width: 100%;

            &::before {
              content: '';
              position: absolute;
              bottom: -4px;
              left: 0;
              width: 0;
              height: 2px;
              background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
              transition: width 0.3s ease;
            }

            &:hover::before {
              width: 100%;
            }
          }
        }

        .ant-form-item-control {
          flex: 1;

          .ant-form-item-control-input {
            min-height: 32px;

            .ant-input,
            .ant-input-number {
              border-radius: 8px;
              transition: all 0.3s ease;
              border: 2px solid #e1e5e9;
              background: rgba(255, 255, 255, 0.9);
              font-size: 14px;

              &:hover {
                border-color: #667eea;
                box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
                transform: translateY(-1px);
              }

              &:focus {
                border-color: #667eea;
                box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
                transform: translateY(-1px);
              }
            }

            .ant-input-number {
              width: 100%;
            }
          }
        }

        // 必填字段样式
        &.ant-form-item-required {
          border-left: 4px solid #667eea;

          .ant-form-item-label label {
            &::after {
              content: ' *';
              color: #ff4757;
              font-weight: bold;
            }
          }
        }

        // 文本域特殊样式
        &:has(.ant-input-textarea) {
          .ant-input {
            border-radius: 8px;
            resize: vertical;
            min-height: 100px;
            line-height: 1.6;
            font-family: inherit;
          }
        }

        // 错误状态样式
        &.ant-form-item-has-error {
          border-left: 4px solid #ff4757;
          background: rgba(255, 71, 87, 0.05);

          .ant-form-item-explain {
            margin-top: 6px;
            font-size: 12px;
            color: #ff4757;
            font-weight: 500;
          }

          .ant-form-item-control-input {
            .ant-input,
            .ant-input-number {
              border-color: #ff4757;
              box-shadow: 0 0 0 3px rgba(255, 71, 87, 0.2);
            }
          }
        }

        // 成功状态样式
        &.ant-form-item-has-success {
          border-left: 4px solid #2ed573;

          .ant-form-item-control-input {
            .ant-input,
            .ant-input-number {
              border-color: #2ed573;
            }
          }
        }
      }

      // 第一个字段特殊样式
      .ant-form-item:first-child {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
      }

      // 最后一个字段特殊样式
      .ant-form-item:last-child {
        background: linear-gradient(135deg, rgba(45, 213, 115, 0.1) 0%, rgba(102, 126, 234, 0.1) 100%);
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    :deep(.room-modal) {
      .ant-modal {
        margin: 16px;
        max-width: calc(100vw - 32px);
      }

      .ant-form {
        .ant-form-item {
          display: block !important;

          .ant-form-item-label {
            text-align: left !important;
            padding-right: 0 !important;
            padding-bottom: 8px !important;
            flex: none !important;
            width: auto !important;
            min-width: auto !important;
          }

          .ant-form-item-control {
            flex: none !important;
          }
        }
      }
    }
  }
</style>
