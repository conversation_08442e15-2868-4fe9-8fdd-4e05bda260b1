import { defHttp } from '/@/utils/http/axios';

enum Api {
  WaterMeterList = '/baWatermeter',
  WaterMeterHistory = '/baWatermeterHistory',
  YesterdayWater = '/baWatermeter/yesterdayWater',
}

/**
 * 获取水表实时数据
 */
export const getWaterMeterList = () => {
  return defHttp.get({ url: Api.WaterMeterList });
};

/**
 * 获取水表历史数据
 */
export const getWaterMeterHistory = (params?: any) => {
  return defHttp.get({ url: Api.WaterMeterHistory, params });
};

/**
 * 获取昨日用水量
 */
export const getYesterdayWater = () => {
  return defHttp.get({ url: Api.YesterdayWater });
};
