/**
 * 初始化优化测试脚本
 * 用于验证项目初始化时不会过早触发可交互对象收集
 */

class InitializationOptimizationTester {
  constructor() {
    this.initCallCount = 0;
    this.rebuildCallCount = 0;
    this.isMonitoring = false;
    this.startTime = 0;
    this.callHistory = [];

    this.init();
  }

  init() {
    window.initOptimizationTester = this;
    console.log('[InitOptimizationTester] 初始化优化测试器已初始化');
    console.log('使用 window.initOptimizationTester.startMonitoring() 开始监控');
  }

  startMonitoring() {
    if (this.isMonitoring) {
      console.log('[InitOptimizationTester] 监控已在运行中');
      return;
    }

    console.log('[InitOptimizationTester] 开始监控初始化过程...');
    this.isMonitoring = true;
    this.initCallCount = 0;
    this.rebuildCallCount = 0;
    this.startTime = Date.now();
    this.callHistory = [];

    // 监控 ModelLoaderManager 的方法调用
    this.monitorModelLoaderManager();

    // 监控 ObjectSelection 的初始化
    this.monitorObjectSelection();

    // 设置自动停止监控
    setTimeout(() => {
      if (this.isMonitoring) {
        this.stopMonitoring();
      }
    }, 10000); // 10秒后自动停止
  }

  stopMonitoring() {
    if (!this.isMonitoring) return;

    this.isMonitoring = false;
    const duration = Date.now() - this.startTime;

    console.log(`[InitOptimizationTester] 监控结束，持续时间: ${duration}ms`);
    console.log(`[InitOptimizationTester] getInteractableObjects 调用次数: ${this.initCallCount}`);
    console.log(`[InitOptimizationTester] _rebuildInteractableObjects 调用次数: ${this.rebuildCallCount}`);

    this.generateReport();
  }

  monitorModelLoaderManager() {
    try {
      // 检查是否存在 ModelLoaderManager
      if (window.ModelLoaderManager && window.ModelLoaderManager.getInstance) {
        const modelLoader = window.ModelLoaderManager.getInstance();

        // 监控 getInteractableObjects 方法
        if (modelLoader.getInteractableObjects) {
          const originalGetInteractableObjects = modelLoader.getInteractableObjects.bind(modelLoader);
          modelLoader.getInteractableObjects = () => {
            this.initCallCount++;
            const timestamp = Date.now() - this.startTime;
            const stackTrace = new Error().stack;

            this.callHistory.push({
              method: 'getInteractableObjects',
              timestamp,
              stackTrace: stackTrace?.split('\n').slice(1, 5).join('\n') || 'No stack trace',
            });

            console.log(`[InitOptimizationTester] getInteractableObjects 被调用 (第${this.initCallCount}次) at ${timestamp}ms`);
            return originalGetInteractableObjects();
          };
        }

        // 监控 _rebuildInteractableObjects 方法
        if (modelLoader._rebuildInteractableObjects) {
          const originalRebuildInteractableObjects = modelLoader._rebuildInteractableObjects.bind(modelLoader);
          modelLoader._rebuildInteractableObjects = () => {
            this.rebuildCallCount++;
            const timestamp = Date.now() - this.startTime;
            const stackTrace = new Error().stack;

            this.callHistory.push({
              method: '_rebuildInteractableObjects',
              timestamp,
              stackTrace: stackTrace?.split('\n').slice(1, 5).join('\n') || 'No stack trace',
            });

            console.log(`[InitOptimizationTester] _rebuildInteractableObjects 被调用 (第${this.rebuildCallCount}次) at ${timestamp}ms`);
            return originalRebuildInteractableObjects();
          };
        }
      }
    } catch (error) {
      console.error('[InitOptimizationTester] 监控 ModelLoaderManager 失败:', error);
    }
  }

  monitorObjectSelection() {
    try {
      // 监控 ObjectSelection 的初始化
      if (window.ObjectSelection) {
        const originalGetInstance = window.ObjectSelection.getInstance;
        if (originalGetInstance) {
          window.ObjectSelection.getInstance = () => {
            const timestamp = Date.now() - this.startTime;
            console.log(`[InitOptimizationTester] ObjectSelection.getInstance 被调用 at ${timestamp}ms`);

            this.callHistory.push({
              method: 'ObjectSelection.getInstance',
              timestamp,
              stackTrace: new Error().stack?.split('\n').slice(1, 5).join('\n') || 'No stack trace',
            });

            return originalGetInstance.call(window.ObjectSelection);
          };
        }
      }
    } catch (error) {
      console.error('[InitOptimizationTester] 监控 ObjectSelection 失败:', error);
    }
  }

  generateReport() {
    const report = {
      summary: {
        duration: Date.now() - this.startTime,
        getInteractableObjectsCalls: this.initCallCount,
        rebuildInteractableObjectsCalls: this.rebuildCallCount,
        totalCalls: this.callHistory.length,
      },
      callHistory: this.callHistory,
      analysis: this.analyzeResults(),
      recommendations: this.getRecommendations(),
    };

    console.log('[InitOptimizationTester] 详细报告:', report);
    return report;
  }

  analyzeResults() {
    const analysis = {
      isOptimized: this.initCallCount <= 2 && this.rebuildCallCount <= 1,
      earlyCallsDetected: this.callHistory.filter((call) => call.timestamp < 1000).length,
      redundantCalls: this.initCallCount > 2,
      performanceImpact: this.rebuildCallCount > 2 ? 'high' : this.rebuildCallCount > 1 ? 'medium' : 'low',
    };

    if (analysis.isOptimized) {
      console.log('✅ [InitOptimizationTester] 初始化优化良好');
    } else {
      console.warn('⚠️ [InitOptimizationTester] 检测到初始化性能问题');
    }

    return analysis;
  }

  getRecommendations() {
    const recommendations = [];

    if (this.initCallCount > 3) {
      recommendations.push('减少 getInteractableObjects 的调用次数，考虑延迟初始化');
    }

    if (this.rebuildCallCount > 2) {
      recommendations.push('减少 _rebuildInteractableObjects 的调用，避免重复重建');
    }

    const earlyCalls = this.callHistory.filter((call) => call.timestamp < 500);
    if (earlyCalls.length > 0) {
      recommendations.push('避免在模型加载完成前过早调用可交互对象相关方法');
    }

    if (recommendations.length === 0) {
      recommendations.push('初始化性能良好，无需优化');
    }

    return recommendations;
  }

  // 手动触发测试场景
  simulateInitialization() {
    console.log('[InitOptimizationTester] 模拟初始化场景...');

    this.startMonitoring();

    // 模拟组件初始化序列
    setTimeout(() => {
      console.log('[InitOptimizationTester] 模拟 SceneController 初始化...');
      if (window.ObjectSelection && window.ObjectSelection.getInstance) {
        window.ObjectSelection.getInstance();
      }
    }, 100);

    setTimeout(() => {
      console.log('[InitOptimizationTester] 模拟模型加载完成...');
      window.dispatchEvent(new CustomEvent('scene-objects-changed'));
    }, 1000);

    setTimeout(() => {
      this.stopMonitoring();
    }, 2000);
  }

  // 获取当前状态
  getCurrentStatus() {
    const status = {
      isMonitoring: this.isMonitoring,
      callCounts: {
        getInteractableObjects: this.initCallCount,
        rebuildInteractableObjects: this.rebuildCallCount,
      },
      recentCalls: this.callHistory.slice(-5),
      uptime: this.isMonitoring ? Date.now() - this.startTime : 0,
    };

    console.log('[InitOptimizationTester] 当前状态:', status);
    return status;
  }
}

// 自动初始化测试器
if (typeof window !== 'undefined') {
  new InitializationOptimizationTester();
}
