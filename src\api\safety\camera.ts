import { defHttp } from '/@/utils/http/axios';

interface CameraItem {
  id: number;
  ip: string;
  rtspUrl: string;
  name: string;
  username: string;
  password: string;
  createDate: string;
}

interface CameraListResult {
  success: boolean;
  message: string;
  code: number;
  result: {
    records: CameraItem[];
    total: number;
    size: number;
    current: number;
    pages: number;
  };
  timestamp: number;
}

export function getCameraList(params?: any) {
  return defHttp.get<CameraListResult>(
    {
      url: '/haikangCa',
      params: {
        pageNo: params?.current || 1,
        pageSize: params?.pageSize || 10,
        ...params,
      },
    },
    {
      errorMessageMode: 'none',
      successMessageMode: 'none',
    }
  );
}

/**
 * 获取摄像头列表(新接口)
 */
export function getCameraListNew() {
  return defHttp.get<CameraListResult>(
    {
      url: '/haikangCa/list',
    },
    {
      errorMessageMode: 'none',
      successMessageMode: 'none',
    }
  );
}

/**
 * 获取摄像头详情
 * @param id 摄像头ID
 * @returns 摄像头详细信息
 */
export function getCameraDetail(id: string) {
  return defHttp.get({
    url: `/haikangCa/${id}`,
  });
}

/**
 * 获取摄像头预览URL
 * @param cameraIndexCode 摄像头索引码
 */
export function _getCameraPreviewURLs(cameraIndexCode: string) {
  console.log('演示环境: 请求预览URL，摄像头编号:', cameraIndexCode);
  // 直接返回成功的测试视频
  return Promise.resolve({
    code: 200,
    success: true,
    message: 'ok',
    result: {
      data: {
        url: '/test-video.mp4',
      },
      hls: '/test-video.mp4',
    },
  });
}

/**
 * 获取测试视频流地址 (仅开发环境使用)
 */
export const getTestStreamUrl = () => {
  return '/test-video.mp4';
};

interface AddCameraResponse {
  success: boolean;
  message: string;
  code: number;
  result: null;
  timestamp: number;
}

/**
 * 新增摄像头
 * @param params 摄像头信息
 */
export function addCamera(params: any) {
  return defHttp.post<AddCameraResponse>(
    {
      url: '/haikangCa',
      data: params,
    },
    {
      // 不进行任何转换，直接返回原始响应数据
      isTransformResponse: false,
      // 不显示成功消息
      successMessageMode: 'none',
    }
  );
}

/**
 * 修改摄像头
 * @param params 摄像头信息
 */
export function updateCamera(params: any) {
  return defHttp.put({
    url: '/haikangCa',
    data: params,
  });
}

/**
 * 删除摄像头
 * @param idList 摄像头ID列表
 */
export function deleteCamera(idList: string[]) {
  const idStr = idList.join(',');
  return defHttp.delete(
    {
      url: `/haikangCa?idList=${idStr}`,
    },
    {
      // 不进行任何转换，直接返回原始响应数据
      isTransformResponse: false,
      // 不显示成功消息
      successMessageMode: 'none',
    }
  );
}
