<template>
  <div ref="chart" class="w-full h-full"></div>
</template>

<script setup>
  import * as echarts from 'echarts';
  import { onMounted, ref } from 'vue';

  const chart = ref(null);
  let chartInstance = null;

  // 修改配色方案为项目主题色
  const color = ['#3B8EE6', '#77A9E3'];

  const xAxisData = ['1', '2', '3', '4', '5', '6', '7', '8'];
  const yAxisData1 = [100, 138, 350, 173, 180, 150, 180, 230];
  const yAxisData2 = [233, 233, 200, 180, 199, 233, 210, 180];

  const hexToRgba = (hex, opacity) => {
    let rgbaColor = '';
    const reg = /^#[\da-f]{6}$/i;
    if (reg.test(hex)) {
      rgbaColor = `rgba(${parseInt('0x' + hex.slice(1, 3))},${parseInt('0x' + hex.slice(3, 5))},${parseInt('0x' + hex.slice(5, 7))},${opacity})`;
    }
    return rgbaColor;
  };

  const option = {
    color: color,
    legend: {
      top: 0,
      itemWidth: 8, // 减小图例标记的宽度
      itemHeight: 8, // 减小图例标记的高度
      itemGap: 4, // 减小图例项间距
      textStyle: {
        color: '#fff', // 修改图例文字颜色
        fontSize: 8, // 减小图例文字大小
      },
    },
    tooltip: {
      trigger: 'axis',
      appendToBody: true,
      formatter: function (params) {
        let html = '';
        params.forEach((v) => {
          html += `<div style="color: #fff;font-size: 12px;line-height: 20px">
                  <span style="display:inline-block;margin-right:5px;border-radius:10px;width:8px;height:8px;background-color:${color[v.componentIndex]};"></span>
                  ${v.seriesName} ${v.name}  
                  <span style="color:${color[v.componentIndex]};font-weight:500;font-size: 14px;margin-left:5px">${v.value}</span>
                  万元`;
        });
        return html;
      },
      extraCssText: 'background: rgba(0,0,0,0.7); border-radius: 4px; border: none; box-shadow: 0 0 3px rgba(0, 0, 0, 0.2); color: #fff;',
      axisPointer: {
        type: 'shadow',
        shadowStyle: {
          color: 'rgba(0,0,0,0.1)',
          shadowColor: 'rgba(0,0,0,0.1)',
          shadowBlur: 5,
        },
      },
    },
    grid: {
      top: '15%', // 调整上边距
      bottom: '10%',
      left: '3%', // 调整左边距
      right: '4%', // 调整右边距
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        axisLabel: {
          formatter: '{value}',
          color: 'rgba(255, 255, 255, 0.7)', // 修改x轴文字颜色
          fontSize: 8, // 减小文字大小
        },
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)', // 修改坐标轴颜色
          },
        },
        data: xAxisData,
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '万/亿KWh',
        axisLabel: {
          color: 'rgba(255, 255, 255, 0.7)', // 修改y轴文字颜色
          fontSize: 8, // 减小文字大小
        },
        nameTextStyle: {
          color: 'rgba(255, 255, 255, 0.7)', // 修改单位文字颜色
          fontSize: 8, // 减小文字大小
          padding: [0, 0, 0, -24], // 调整单位位置
        },
        splitLine: {
          lineStyle: {
            type: 'solid',
            color: 'rgba(255, 255, 255, 0.1)', // 修改分割线颜色
          },
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
      },
    ],
    series: [
      {
        // 预测出电量
        name: '预测出电量',
        type: 'line',
        smooth: true,
        symbolSize: 6, // 减小点的大小
        zlevel: 3,
        lineStyle: {
          color: color[0],
          shadowBlur: 0, // 移除阴影效果
        },
        symbol: 'circle',
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(59, 142, 230, 0.3)' },
            { offset: 1, color: 'rgba(59, 142, 230, 0.1)' },
          ]),
        },
        data: yAxisData1,
      },
      {
        // 实际用电量
        name: '实际用电量',
        type: 'line',
        smooth: true,
        symbolSize: 6, // 减小点的大小
        zlevel: 3,
        lineStyle: {
          color: color[1],
          shadowBlur: 0, // 移除阴影效果
        },
        symbol: 'circle',
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(119, 169, 227, 0.3)' },
            { offset: 1, color: 'rgba(119, 169, 227, 0.1)' },
          ]),
        },
        data: yAxisData2,
      },
    ],
  };

  onMounted(() => {
    if (chart.value) {
      chartInstance = echarts.init(chart.value);
      chartInstance.setOption(option);
    }
  });
</script>
