import { defHttp } from '/@/utils/http/axios';

enum Api {
  DEVICE_DATA_LIST = '/baDeviceData/device',
  DEVICE_DATA_HISTORY = '/baDeviceData/code/history',
}

// 设备数据项
export interface DeviceDataItem {
  id?: string;
  name: string;
  remark: string;
  dataTime: Date;
  valueData: string;
}

// 查询参数
export interface DeviceDataParams {
  pageNo: number;
  pageSize: number;
  name?: string;
  startTime?: string;
  endTime?: string;
}

// 设备历史数据查询参数
export interface DeviceHistoryParams {
  /**
   * 主键
   */
  id: string;
  /**
   * 1  ba_device_data     2 ba_forward_device 3 real_time_data
   */
  type: number;
  /**
   * 1 ,7 ,14,30
   */
  day?: number;
}

// 历史数据点
export interface HistoryDataPoint {
  time: string;
  value: number;
}

// API返回的原始历史数据项
export interface RawHistoryDataItem {
  id?: string;
  name?: string;
  remark?: string;
  unit?: string;
  dataTime?: string;
  time?: string;
  valueData?: string;
  value?: number;
}

// 历史数据响应
export interface DeviceHistoryResponse {
  id: string;
  name: string;
  remark: string;
  unit?: string;
  data: HistoryDataPoint[];
}

// 获取设备数据列表
export const getDeviceDataList = (params: DeviceDataParams) =>
  defHttp.get<{
    records: DeviceDataItem[];
    total: number;
    pages: number;
    current: number;
    size: number;
  }>({
    url: Api.DEVICE_DATA_LIST,
    params,
  });

// 获取设备历史数据
export const getDeviceHistoryData = (params: DeviceHistoryParams) => {
  // 开发环境日志
  if (process.env.NODE_ENV === 'development') {
    console.log('[API] getDeviceHistoryData 调用，参数:', params);
  }

  return defHttp
    .post<RawHistoryDataItem[]>({
      url: Api.DEVICE_DATA_HISTORY,
      data: params,
    })
    .then((response) => {
      if (process.env.NODE_ENV === 'development') {
        console.log('[API] getDeviceHistoryData 响应数据量:', response?.length || 0);
      }
      return response;
    })
    .catch((error) => {
      console.error('[API] getDeviceHistoryData 错误:', error);
      throw error;
    });
};
