﻿<!doctype html>
<html lang="zh-CN">

<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>动力与环境平台</title>
	<script type="text/javascript" src="js/jquery.js"></script>
	<script type="text/javascript" src="js/echarts.min.js"></script>
	<!-- 先加载API工具，再加载业务逻辑 -->
	<script type="text/javascript" src="js/api.js"></script>
	<script type="text/javascript" src="js/jingmen-map.js"></script>
	<script type="text/javascript" src="js/js.js"></script>
	<link rel="stylesheet" href="css/style.css">
	<!-- 添加Font Awesome图标库 -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
	<style>
		/* 测点类型统计样式 */
		.point-type-stats {
			display: flex;
			flex-direction: column;
			gap: 5px;
			margin-top: 5px;
			max-height: 120px; /* 限制最大高度 */
			overflow-y: auto; /* 如果内容过多，添加滚动条 */
			padding-right: 2px; /* 为滚动条留出空间 */
		}

		/* 动环实时数据样式 */
		.real-time-data-container {
			display: flex;
			flex-direction: column;
			gap: 15px; /* 增加卡片之间的间距 */
			height: 100%;
			overflow-y: auto;
			padding: 10px; /* 增加容器内边距 */
		}

		.real-time-data-card {
			background: rgba(3, 60, 118, 0.5);
			border: 1px solid #1070aa;
			border-radius: 6px; /* 增加圆角 */
			padding: 15px; /* 增加卡片内边距 */
			transition: all 0.2s ease;
			position: relative;
			overflow: hidden;
			min-height: 90px; /* 设置最小高度 */
			box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); /* 添加阴影效果 */
		}

		.real-time-data-card:hover {
			background: rgba(3, 60, 118, 0.8);
			border-color: #00deff;
			transform: translateY(-2px);
			box-shadow: 0 4px 8px rgba(0, 222, 255, 0.3);
		}

		.real-time-data-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 12px; /* 增加头部与内容的间距 */
			border-bottom: 1px solid rgba(16, 112, 170, 0.3); /* 添加分隔线 */
			padding-bottom: 8px; /* 分隔线下方间距 */
		}

		.real-time-data-device {
			font-size: 15px; /* 增大字体 */
			font-weight: bold;
			color: #fff;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			max-width: 70%;
		}

		.real-time-data-time {
			font-size: 12px;
			color: rgba(255, 255, 255, 0.6);
			white-space: nowrap;
			background: rgba(0, 0, 0, 0.2); /* 添加背景色 */
			padding: 2px 6px; /* 添加内边距 */
			border-radius: 10px; /* 圆角 */
		}

		.real-time-data-content {
			display: flex;
			align-items: center;
			gap: 15px; /* 增加内容元素间距 */
			padding-top: 5px; /* 顶部间距 */
		}

		.real-time-data-value-container {
			flex: 1;
		}

		.real-time-data-signal {
			font-size: 14px; /* 增大字体 */
			color: rgba(255, 255, 255, 0.8);
			margin-bottom: 8px; /* 增加与数值的间距 */
		}

		.real-time-data-value {
			display: flex;
			align-items: baseline;
		}

		.real-time-data-number {
			font-size: 22px; /* 增大字体 */
			font-weight: bold;
			color: #00deff;
			margin-right: 5px;
			text-shadow: 0 0 5px rgba(0, 222, 255, 0.5); /* 添加发光效果 */
		}

		.real-time-data-unit {
			font-size: 13px; /* 增大字体 */
			color: rgba(255, 255, 255, 0.7);
		}

		.real-time-data-icon {
			width: 50px; /* 增加图标尺寸 */
			height: 50px; /* 增加图标尺寸 */
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 50%;
			background: rgba(0, 222, 255, 0.2); /* 增加背景色透明度 */
			color: #00deff;
			font-size: 22px; /* 增大图标字体 */
			box-shadow: 0 0 10px rgba(0, 222, 255, 0.3); /* 添加发光效果 */
			border: 1px solid rgba(0, 222, 255, 0.5); /* 添加边框 */
			margin-right: 5px; /* 添加右侧间距 */
		}

		.real-time-data-more {
			background: rgba(3, 60, 118, 0.3);
			border: 1px solid #1070aa;
			border-radius: 4px;
			padding: 8px;
			margin-top: 10px;
			text-align: center;
		}

		.real-time-data-more-text {
			font-size: 12px;
			color: rgba(255, 255, 255, 0.7);
		}

		/* 自定义滚动条样式 */
		.real-time-data-container::-webkit-scrollbar,
		#realTimeDataContainer::-webkit-scrollbar {
			width: 6px; /* 增加滚动条宽度 */
		}

		.real-time-data-container::-webkit-scrollbar-track,
		#realTimeDataContainer::-webkit-scrollbar-track {
			background: rgba(0, 0, 0, 0.2); /* 增加轨道背景色 */
			border-radius: 3px;
		}

		.real-time-data-container::-webkit-scrollbar-thumb,
		#realTimeDataContainer::-webkit-scrollbar-thumb {
			background: rgba(0, 222, 255, 0.6); /* 使用亮蓝色 */
			border-radius: 3px;
			box-shadow: 0 0 5px rgba(0, 222, 255, 0.3); /* 添加发光效果 */
		}

		.real-time-data-container::-webkit-scrollbar-thumb:hover,
		#realTimeDataContainer::-webkit-scrollbar-thumb:hover {
			background: rgba(0, 222, 255, 0.8); /* 悬停时更亮 */
		}

		/* 分页控件样式 */
		.pagination-container {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 8px 15px;
			background: rgba(3, 60, 118, 0.3);
			border-top: 1px solid rgba(16, 112, 170, 0.5);
		}

		.pagination-info {
			color: rgba(255, 255, 255, 0.8);
			font-size: 13px;
		}

		.pagination-controls {
			display: flex;
			gap: 10px;
		}

		.pagination-btn {
			width: 90px;
			height: 30px;
			border-radius: 4px;
			background: rgba(0, 222, 255, 0.1);
			border: 1px solid rgba(0, 222, 255, 0.3);
			color: #00deff;
			display: flex;
			align-items: center;
			justify-content: center;
			cursor: pointer;
			transition: all 0.2s ease;
			padding: 0;
			font-size: 12px;
		}

		.pagination-btn:hover:not(:disabled) {
			background: rgba(0, 222, 255, 0.3);
		}

		.pagination-btn:disabled {
			opacity: 0.5;
			cursor: not-allowed;
		}

		/* 实时数据表格样式 */
		.real-time-data-table {
			width: 100%;
			border-collapse: collapse;
		}

		.real-time-data-table-header {
			display: grid;
			grid-template-columns: 70% 30%;
			background: rgba(0, 222, 255, 0.1);
			border-bottom: 1px solid rgba(0, 222, 255, 0.3);
			padding: 8px 0;
			font-weight: bold;
			color: rgba(255, 255, 255, 0.9);
		}

		.real-time-data-table-body {
			max-height: calc(100% - 40px);
			overflow-y: auto;
		}

		.real-time-data-table-row {
			display: grid;
			grid-template-columns: 70% 30%;
			border-bottom: 1px solid rgba(16, 112, 170, 0.2);
			padding: 10px 0;
			transition: background 0.2s ease;
		}

		.real-time-data-table-row:hover {
			background: rgba(0, 222, 255, 0.05);
		}

		.real-time-data-table-cell {
			padding: 0 10px;
			display: flex;
			flex-direction: column;
			justify-content: center;
		}

		.real-time-data-table-cell.signal-name {
			overflow: hidden;
		}

		.real-time-data-table-cell.value {
			font-weight: bold;
			color: #00deff;
			font-size: 16px;
			text-align: right;
			justify-content: center;
			align-items: flex-end;
		}

		.device-name {
			font-size: 12px;
			color: rgba(255, 255, 255, 0.6);
			margin-bottom: 4px;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}

		.signal-name {
			font-size: 14px;
			color: rgba(255, 255, 255, 0.9);
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}


		.point-type-item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 2px 5px;
			background: rgba(30, 42, 71, 0.3);
			border-radius: 4px;
			min-height: 22px; /* 确保每项有足够的高度 */
		}
		.point-type-name {
			font-size: 12px;
			color: rgba(255, 255, 255, 0.8);
			white-space: nowrap; /* 防止文本换行 */
			margin-right: 5px; /* 与数值保持一定距离 */
		}
		.point-type-value {
			font-size: 12px;
			color: #0580f2;
			font-weight: bold;
			white-space: nowrap; /* 防止文本换行 */
			text-align: right; /* 数值右对齐 */
		}
		/* 自定义滚动条样式 */
		.point-type-stats::-webkit-scrollbar {
			width: 4px;
		}
		.point-type-stats::-webkit-scrollbar-track {
			background: rgba(0, 0, 0, 0.1);
			border-radius: 2px;
		}
		.point-type-stats::-webkit-scrollbar-thumb {
			background: rgba(5, 128, 242, 0.5);
			border-radius: 2px;
		}
		/* 调整测点统计容器样式 */
		.point-stats-container {
			height: 100%;
			display: flex;
			flex-direction: column;
			padding: 5px;
			box-sizing: border-box;
		}
		/* 调整测点总数的样式 */
		.total-points {
			margin-bottom: 5px;
			text-align: center;
		}
		.total-points h3 {
			font-size: 24px;
			margin: 0;
			line-height: 1.2;
			color: #0580f2;
		}
		.total-points span {
			font-size: 12px;
			color: rgba(255, 255, 255, 0.8);
		}
		/* 确保饼图容器有足够的空间 */
		#echart8 {
			height: 100%;
			min-height: 200px;
		}

		/* 动环告警样式 */
		.alarm-container {
			height: calc(100% - 40px);
			overflow-y: auto;
			padding: 5px;
			background: rgba(3, 43, 123, 0.3);
			border: 1px solid #1070aa;
			border-radius: 4px;
		}

		.alarm-item {
			margin-bottom: 8px;
			padding: 8px 10px;
			background: rgba(3, 60, 118, 0.5);
			border: 1px solid #1070aa;
			border-radius: 4px;
			transition: all 0.2s ease;
		}

		.alarm-item:hover {
			background: rgba(3, 60, 118, 0.8);
			border-color: #00deff;
			transform: translateY(-2px);
			box-shadow: 0 2px 6px rgba(0, 222, 255, 0.2);
		}

		.alarm-header {
			display: flex;
			justify-content: space-between;
			margin-bottom: 5px;
		}

		.alarm-device {
			font-size: 14px;
			font-weight: bold;
			color: #fff;
		}

		.alarm-level {
			padding: 2px 6px;
			border-radius: 2px;
			font-size: 12px;
		}

		.alarm-level-1 {
			background: rgba(255, 0, 0, 0.2);
			color: #ff0000;
		}

		.alarm-level-2 {
			background: rgba(255, 165, 0, 0.2);
			color: #ffa500;
		}

		.alarm-level-3 {
			background: rgba(255, 255, 0, 0.2);
			color: #ffff00;
		}

		.alarm-level-4 {
			background: rgba(0, 0, 255, 0.2);
			color: #0000ff;
		}

		.alarm-desc {
			font-size: 13px;
			color: rgba(255, 255, 255, 0.8);
			margin-bottom: 5px;
		}

		.alarm-footer {
			display: flex;
			justify-content: space-between;
			font-size: 12px;
			color: rgba(255, 255, 255, 0.6);
		}

		.alarm-time {
			text-align: right;
		}

		.alarm-pagination {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 8px 5px;
			margin-top: 5px;
			background: rgba(3, 43, 123, 0.3);
			border: 1px solid #1070aa;
			border-radius: 4px;
		}

		.pagination-info {
			font-size: 12px;
			color: rgba(255, 255, 255, 0.8);
		}

		.pagination-controls {
			display: flex;
			gap: 10px;
		}

		.pagination-btn {
			background: rgba(5, 128, 242, 0.3);
			border: 1px solid #1070aa;
			color: #fff;
			padding: 3px 10px;
			border-radius: 3px;
			cursor: pointer;
			font-size: 12px;
			transition: all 0.2s ease;
		}

		.pagination-btn:hover:not(:disabled) {
			background: rgba(5, 128, 242, 0.6);
		}

		.pagination-btn:disabled {
			opacity: 0.5;
			cursor: not-allowed;
		}

		/* 自定义滚动条样式 */
		.alarm-container::-webkit-scrollbar {
			width: 4px;
		}

		.alarm-container::-webkit-scrollbar-track {
			background: rgba(0, 0, 0, 0.1);
			border-radius: 2px;
		}

		.alarm-container::-webkit-scrollbar-thumb {
			background: rgba(5, 128, 242, 0.5);
			border-radius: 2px;
		}
	</style>
	<!-- 接收主应用传递的token -->
	<script type="text/javascript">
		// 定义全局变量存储token
		window.portalToken = '';

		// 从主应用接收token的函数
		function receiveTokenFromMainApp() {
			// 判断是否在微前端环境中
			if (window.$wujie && window.$wujie.props && window.$wujie.props.data) {
				// 从主应用获取token
				const token = window.$wujie.props.data.token;
				if (token) {
					// 存储token到全局变量
					window.portalToken = token;
					// 同时存储到localStorage，以便页面刷新后仍能使用
					localStorage.setItem('portal_token', token);

					// 详细日志
					console.log('%c从主应用成功接收到token!', 'background: #4CAF50; color: white; padding: 4px 8px; border-radius: 4px;');
					console.log('Token值: ' + token.substring(0, 10) + '...' + token.substring(token.length - 10));
				} else {
					console.warn('%c从主应用获取token失败: token为空', 'background: #FFC107; color: black; padding: 4px 8px; border-radius: 4px;');
				}
			} else {
				// 如果不在微前端环境中，尝试从localStorage获取
				const savedToken = localStorage.getItem('portal_token');
				if (savedToken) {
					window.portalToken = savedToken;
					console.log('%c从localStorage获取到token', 'background: #2196F3; color: white; padding: 4px 8px; border-radius: 4px;');
					console.log('Token值: ' + savedToken.substring(0, 10) + '...' + savedToken.substring(savedToken.length - 10));
				} else {
					console.warn('%c未能获取到token', 'background: #F44336; color: white; padding: 4px 8px; border-radius: 4px;');
				}
			}
		}

		// 获取token的辅助函数，供API调用使用
		function getPortalToken() {
			return window.portalToken || localStorage.getItem('portal_token') || '';
		}

		// 页面加载完成后执行
		document.addEventListener('DOMContentLoaded', function () {
			console.log('%c子应用页面加载完成 - DOMContentLoaded', 'background: #4CAF50; color: white; padding: 4px 8px; border-radius: 4px;');
			receiveTokenFromMainApp();
		});

		// 添加window.onload事件，确保所有资源都加载完成
		window.onload = function () {
			console.log('%c子应用所有资源加载完成 - window.onload', 'background: #2196F3; color: white; padding: 4px 8px; border-radius: 4px;');
			// 手动触发API调用
			if (typeof loadAllApiData === 'function') {
				console.log('%c手动触发API数据加载', 'background: #9C27B0; color: white; padding: 4px 8px; border-radius: 4px;');
				loadAllApiData();
			} else {
				console.error('%c找不到loadAllApiData函数', 'background: #F44336; color: white; padding: 4px 8px; border-radius: 4px;');
			}
		};
	</script>
</head>

<body>
	<div class="head clearfix">
		<h1 class="">动力与环境平台</h1>
		<div class="time" id="showTime">2023/5/12 17:00:12</div>
		<div class="name"><a href="#">荆门移动数据中心</a></div>
		<script>
			var t = null;
			t = setTimeout(time, 1000);//开始运行
			function time() {
				clearTimeout(t);//清除定时器
				dt = new Date();
				var y = dt.getFullYear();
				var mt = dt.getMonth() + 1;
				var day = dt.getDate();
				var h = dt.getHours();//获取时
				var m = dt.getMinutes();//获取分
				var s = dt.getSeconds();//获取秒
				document.getElementById("showTime").innerHTML = y + "/" + mt + "/" + day + " " + h + ":" + m + ":" + s + "";
				t = setTimeout(time, 1000); //设定定时器，循环运行
			}

		</script>
	</div>
	<div class="mainbox">

		<ul class="clearfix nav1" style="height:100%">
			<li style="width: 26%;height:100%">
				<div class="box" style="height: 30%;">
					<div class="tit"><span>资源统计</span>
						<p></p>
					</div>
					<div class="boxnav" style="height: calc(100% - 40px);">
						<!-- 数量统计数据将通过JavaScript动态加载 -->
						<div class="loading-indicator">加载中...</div>
					</div>
				</div>

				<div class="box" style="height: 70%;">
					<div class="tit"><span>机房列表</span>
						<p></p>
					</div>
					<div class="boxnav" style="height: calc(100% - 40px); overflow: hidden;">
						<div class="environment-list-container">
							<div id="environmentList" class="environment-list">
								<!-- 机房列表数据将通过JavaScript动态加载 -->
								<div class="loading-indicator">加载中...</div>
							</div>
						</div>
					</div>
				</div>

			</li>
			<li style="width:48%;height:100%">

				<div class="box" style="height: 50%;">
					<div class="boxnav" style="height: 557px; position: relative;">
						<!-- 荆门市地图容器 -->
						<div id="map-container" style="width: 100%; height: 100%; position: relative;"></div>
					</div>
				</div>
				<div class="box" style="height: 50%;">
					<div class="tit"><span>动环告警</span>
						<p></p>
					</div>
					<div class="boxnav" style="height: 250px;">
						<div id="alarmContainer" class="alarm-container">
							<!-- 告警数据将通过JavaScript动态加载 -->
							<div class="loading-indicator">加载中...</div>
						</div>
						<div class="alarm-pagination">
							<div class="pagination-info">
								<span id="currentPage">1</span>/<span id="totalPages">1</span>页，共<span id="totalRecords">0</span>条
							</div>
							<div class="pagination-controls">
								<button id="prevPage" class="pagination-btn" type="button" disabled>&lt; 上一页</button>
								<button id="nextPage" class="pagination-btn" type="button" disabled>下一页 &gt;</button>
							</div>
						</div>
					</div>
				</div>
			</li>
			<li style="width: 26%;height:100%">
				<div class="box">
					<div class="tit">
						<span>动环实时数据</span>
						<p></p>
					</div>
					<div class="boxnav" id="realTimeDataContainer" style="height: 350px; overflow-y: auto;">
						<!-- 动环实时数据将通过JavaScript动态加载 -->
						<div class="loading-indicator">加载中...</div>
					</div>
					<!-- 分页控件 -->
					<div class="pagination-container">
						<div class="pagination-info">
							<span id="realTimeDataCurrentPageInfo">第 <span id="realTimeDataCurrentPage">1</span> 页，共 <span id="realTimeDataTotalPages">0</span> 页</span>
						</div>
						<div class="pagination-controls">
							<button id="realTimeDataPrevPage" class="pagination-btn" type="button" disabled title="上一页">
								<i class="fas fa-chevron-left"></i>
							</button>
							<button id="realTimeDataNextPage" class="pagination-btn" type="button" disabled title="下一页">
								<i class="fas fa-chevron-right"></i>
							</button>
						</div>
					</div>
				</div>
				<div class="box">
					<div class="tit"><span>测点数排名</span>
						<p></p>
					</div>
					<div class="boxnav" style="height: 180px;" id="echart7">


					</div>
				</div>

				<div class="box">
					<div class="tit"><span>测点数占比</span>
						<p></p>
					</div>
					<div class="boxnav" style="height: 250px;">
						<ul class="jbgc">
							<li style="width:90%" id="echart8"></li>
						</ul>
					</div>
				</div>

			</li>
		</ul>
	</div>

	<!-- 页面底部添加初始化脚本 -->
	<script type="text/javascript">
		// 全局变量，用于跟踪API是否已经加载
		window.apiLoaded = false;

		// 页面卸载时清理资源
		window.addEventListener('unload', function() {
			console.log('%c页面卸载，清理资源', 'background: #795548; color: white; padding: 4px 8px; border-radius: 4px;');
			// 清理资源
			if (window.jingmenMapInstance) {
				window.jingmenMapInstance.dispose();
			}
		});

		// 统一的API调用入口
		function loadPortalApiData() {
			// 如果API已经加载过，不再重复加载
			if (window.apiLoaded) {
				console.log('%cAPI数据已经加载过，不再重复加载', 'background: #9C27B0; color: white; padding: 4px 8px; border-radius: 4px;');
				return;
			}

			console.log('%c开始加载API数据', 'background: #E91E63; color: white; padding: 4px 8px; border-radius: 4px;');

			// 检查API工具是否已加载
			if (!window.portalApi) {
				console.error('%cAPI工具未加载，无法调用API', 'background: #F44336; color: white; padding: 4px 8px; border-radius: 4px;');
				return;
			}

			// 标记API已经加载
			window.apiLoaded = true;

			// 添加API基础路径和当前URL的日志
			console.log('%cAPI基础路径:', 'background: #E91E63; color: white; padding: 2px 4px; border-radius: 2px;', window.portalApi.baseUrl || '未设置');
			console.log('%c当前页面URL:', 'background: #E91E63; color: white; padding: 2px 4px; border-radius: 2px;', window.location.href);

			// 调用API获取资源总览数据
			if (typeof window.portalApi.getRooms === 'function') {
				console.log('%c调用getRooms API', 'background: #E91E63; color: white; padding: 4px 8px; border-radius: 4px;');

				window.portalApi.getRooms()
					.then(function (response) {
						console.log('%cgetRooms API返回结果:', 'background: #4CAF50; color: white; padding: 4px 8px; border-radius: 4px;', response);

						// 更新设备运行状态UI
						updateDeviceStatusUI(response);

						// 如果响应中包含资源总览数据，也更新资源总览UI
						if (response && response.success && response.result &&
							(response.result.onlineDevices !== undefined ||
								response.result.alarmDevices !== undefined ||
								response.result.deviceUtilization !== undefined)) {
							updateRoomsDataUI(response);
						}
					})
					.catch(function (error) {
						console.error('%cgetRooms API调用失败:', 'background: #F44336; color: white; padding: 4px 8px; border-radius: 4px;', error);

						// 使用模拟数据
						const mockResponse = {
							success: true,
							result: {
								roomNum: 21,    // 机房数
								deviceNum: 413, // 设备数
								dhNum: 8657,    // 测点数
								onlineDevices: 182,
								alarmDevices: 5,
								deviceUtilization: 89
							}
						};

						// 更新设备运行状态UI
						updateDeviceStatusUI(mockResponse);

						// 更新资源总览UI
						updateRoomsDataUI(mockResponse);
					});
			}

			// 机房列表API调用已移除
		}

		// 调用API获取测点数占比数据
		if (typeof window.portalApi.getDeviceTypeInfo === 'function') {
			console.log('%c调用getDeviceTypeInfo API', 'background: #E91E63; color: white; padding: 4px 8px; border-radius: 4px;');

			window.portalApi.getDeviceTypeInfo()
				.then(function (response) {
					console.log('%cgetDeviceTypeInfo API返回结果:', 'background: #4CAF50; color: white; padding: 4px 8px; border-radius: 4px;', response);
					// 图表更新逻辑已在echarts_8函数中实现
				})
				.catch(function (error) {
					console.error('%cgetDeviceTypeInfo API调用失败:', 'background: #F44336; color: white; padding: 4px 8px; border-radius: 4px;', error);
				});
		}

		// 调用API获取动环告警数据
		if (typeof window.portalApi.getAlarmData === 'function') {
			console.log('%c调用getAlarmData API', 'background: #E91E63; color: white; padding: 4px 8px; border-radius: 4px;');

			// 初始化分页参数
			const alarmPageParams = {
				pageNo: 1,
				pageSize: 5
			};

			// 加载告警数据
			loadAlarmData(alarmPageParams);

			// 绑定分页按钮事件
			$('#prevPage').on('click', function() {
				if (alarmPageParams.pageNo > 1) {
					alarmPageParams.pageNo--;
					loadAlarmData(alarmPageParams);
				}
			});

			$('#nextPage').on('click', function() {
				const totalPages = parseInt($('#totalPages').text());
				if (alarmPageParams.pageNo < totalPages) {
					alarmPageParams.pageNo++;
					loadAlarmData(alarmPageParams);
				}
			});
		}

		// 加载告警数据的函数
		function loadAlarmData(params) {
			console.log('%c加载告警数据，页码：' + params.pageNo, 'background: #E91E63; color: white; padding: 4px 8px; border-radius: 4px;');

			// 显示加载中
			$('#alarmContainer').html('<div class="loading-indicator">加载中...</div>');

			window.portalApi.getAlarmData(params)
				.then(function(response) {
					console.log('%cgetAlarmData API返回结果:', 'background: #4CAF50; color: white; padding: 4px 8px; border-radius: 4px;', response);

					// 更新UI
					updateAlarmUI(response);
				})
				.catch(function(error) {
					console.error('%cgetAlarmData API调用失败:', 'background: #F44336; color: white; padding: 4px 8px; border-radius: 4px;', error);

					// 显示错误信息
					$('#alarmContainer').html('<div class="loading-indicator">获取告警数据失败</div>');
				});
		}

		// 更新告警UI的函数
		function updateAlarmUI(response) {
			if (response && response.success && response.result) {
				console.log('%c更新告警UI', 'background: #4CAF50; color: white; padding: 4px 8px; border-radius: 4px;');

				// 获取告警容器
				const container = $('#alarmContainer');

				// 清空容器
				container.empty();

				// 获取告警记录
				const records = response.result.records || [];

				// 如果没有数据，显示提示信息
				if (records.length === 0) {
					container.html('<div class="loading-indicator">暂无告警数据</div>');
					return;
				}

				// 遍历告警数据
				records.forEach(function(item) {
					// 获取告警级别名称
					const levelMap = {
						'0': '正常数据',
						'1': '一级告警',
						'2': '二级告警',
						'3': '三级告警',
						'4': '四级告警',
						'5': '操作事件',
						'6': '无效数据'
					};

					const levelName = levelMap[item.alarmLevel] || '未知级别';

					// 创建告警项HTML
					const itemHtml = `
					<div class="alarm-item">
						<div class="alarm-header">
							<div class="alarm-device">${item.deviceName || '未知设备'}</div>
							<div class="alarm-level alarm-level-${item.alarmLevel}">${levelName}</div>
						</div>
						<div class="alarm-desc">${item.alarmDesc || '无告警描述'}</div>
						<div class="alarm-footer">
							<div class="alarm-signal">信号ID: ${item.signalId || 'N/A'}</div>
							<div class="alarm-time">${item.alarmTime || item.createDate || '未知时间'}</div>
						</div>
					</div>
					`;

					// 添加到容器
					container.append(itemHtml);
				});

				// 更新分页信息
				const currentPage = response.result.current || 1;
				const totalPages = response.result.pages || 1;
				const totalRecords = response.result.total || 0;

				$('#currentPage').text(currentPage);
				$('#totalPages').text(totalPages);
				$('#totalRecords').text(totalRecords);

				// 更新分页按钮状态
				$('#prevPage').prop('disabled', currentPage <= 1);
				$('#nextPage').prop('disabled', currentPage >= totalPages);
			} else {
				console.warn('%c响应格式不符合预期，无法更新告警UI', 'background: #FF9800; color: white; padding: 2px 4px; border-radius: 2px;');
				// 显示错误信息
				$('#alarmContainer').html('<div class="loading-indicator">获取告警数据失败</div>');
			}
		}

		// 调用API获取机房列表数据
		if (typeof window.portalApi.getEnvironmentList === 'function') {
			console.log('%c调用getEnvironmentList API', 'background: #E91E63; color: white; padding: 4px 8px; border-radius: 4px;');

			window.portalApi.getEnvironmentList()
				.then(function (response) {
					console.log('%cgetEnvironmentList API返回结果:', 'background: #4CAF50; color: white; padding: 4px 8px; border-radius: 4px;', response);

					// 更新UI
					updateEnvironmentListUI(response);
				})
				.catch(function (error) {
					console.error('%cgetEnvironmentList API调用失败:', 'background: #F44336; color: white; padding: 4px 8px; border-radius: 4px;', error);

					// 使用模拟数据
					const mockResponse = {
						success: true,
						result: [
							{
								siteID: "S001",
								roomName: "主机房A区",
								scid: "SC001",
								roomDesc: "主要服务器机房",
								roomType: "服务器机房",
								roomID: "R001"
							},
							{
								siteID: "S002",
								roomName: "主机房B区",
								scid: "SC002",
								roomDesc: "备用服务器机房",
								roomType: "服务器机房",
								roomID: "R002"
							},
							{
								siteID: "S003",
								roomName: "网络设备区",
								scid: "SC003",
								roomDesc: "网络核心设备区域",
								roomType: "网络机房",
								roomID: "R003"
							},
							{
								siteID: "S004",
								roomName: "存储设备区",
								scid: "SC004",
								roomDesc: "数据存储设备区域",
								roomType: "存储机房",
								roomID: "R004"
							},
							{
								siteID: "S005",
								roomName: "UPS电源室",
								scid: "SC005",
								roomDesc: "不间断电源设备室",
								roomType: "电源机房",
								roomID: "R005"
							},
							{
								siteID: "S006",
								roomName: "配电室",
								scid: "SC006",
								roomDesc: "电力配电设备室",
								roomType: "电源机房",
								roomID: "R006"
							},
							{
								siteID: "S007",
								roomName: "空调机房",
								scid: "SC007",
								roomDesc: "精密空调设备区",
								roomType: "空调机房",
								roomID: "R007"
							}
						]
					};

					updateEnvironmentListUI(mockResponse);
				});
		}

		// 更新资源总览数据UI的函数
		function updateRoomsDataUI(response) {
			if (response && response.success && response.result) {
				console.log('%c更新资源总览UI', 'background: #4CAF50; color: white; padding: 4px 8px; border-radius: 4px;');

				// 更新在线设备数
				if (response.result.onlineDevices !== undefined) {
					$('.data-total .grid-item:nth-child(1) .data-item:nth-child(1) .value').text(response.result.onlineDevices);
				}

				// 更新告警设备数
				if (response.result.alarmDevices !== undefined) {
					$('.data-total .grid-item:nth-child(1) .data-item:nth-child(2) .value').text(response.result.alarmDevices);
				}

				// 更新设备利用率
				if (response.result.deviceUtilization !== undefined) {
					$('.data-total .grid-item:nth-child(1) .data-item:nth-child(3) .value').text(response.result.deviceUtilization);
				}
			} else {
				console.warn('%c响应格式不符合预期，无法更新资源总览UI', 'background: #FF9800; color: white; padding: 2px 4px; border-radius: 2px;');
			}
		}

		// 机房列表UI更新函数已移除

		// 更新设备运行状态UI的函数
		function updateDeviceStatusUI(response) {
			if (response && response.success && response.result) {
				console.log('%c更新资源统计UI', 'background: #4CAF50; color: white; padding: 4px 8px; border-radius: 4px;');

				// 获取资源统计容器
				const container = $('.box .tit span:contains("资源统计")').closest('.box').find('.boxnav');

				// 提取数据
				const { roomNum, deviceNum, dhNum } = response.result;

				// 清空现有内容
				container.empty();

				// 创建新的列表，使用Ant Design图标，一行显示三个字段
				const listHtml = `
				<ul class="stats-row clearfix">
					<li class="stats-item">
						<div class="icon ant-icon-mini">
							<!-- Ant Design HomeOutlined 图标 -->
							<svg viewBox="64 64 896 896" focusable="false" data-icon="home" width="1em" height="1em" fill="currentColor" aria-hidden="true">
								<path d="M946.5 505L560.1 118.8l-25.9-25.9a31.5 31.5 0 00-44.4 0L77.5 505a63.9 63.9 0 00-18.8 46c.4 35.2 29.7 63.3 64.9 63.3h42.5V940h691.8V614.3h43.4c17.1 0 33.2-6.7 45.3-18.8a63.6 63.6 0 0018.7-45.3c0-17-6.7-33.1-18.8-45.2zM568 868H456V664h112v204zm217.9-325.7V868H632V640c0-22.1-17.9-40-40-40H432c-22.1 0-40 17.9-40 40v228H238.1V542.3h-96l370-369.7 23.1 23.1L882 542.3h-96.1z"></path>
							</svg>
						</div>
						<div class="stats-content">
							<span>机房数量</span>
							<p><em>${roomNum || 0}</em><i>个</i></p>
						</div>
					</li>
					<li class="stats-item">
						<div class="icon ant-icon-mini">
							<!-- Ant Design DesktopOutlined 图标 -->
							<svg viewBox="64 64 896 896" focusable="false" data-icon="desktop" width="1em" height="1em" fill="currentColor" aria-hidden="true">
								<path d="M928 140H96c-17.7 0-32 14.3-32 32v496c0 17.7 14.3 32 32 32h380v112H304c-8.8 0-16 7.2-16 16v48c0 4.4 3.6 8 8 8h432c4.4 0 8-3.6 8-8v-48c0-8.8-7.2-16-16-16H548V700h380c17.7 0 32-14.3 32-32V172c0-17.7-14.3-32-32-32zm-40 488H136V212h752v416z"></path>
							</svg>
						</div>
						<div class="stats-content">
							<span>设备数量</span>
							<p><em>${deviceNum || 0}</em><i>台</i></p>
						</div>
					</li>
					<li class="stats-item">
						<div class="icon ant-icon-mini">
							<!-- Ant Design DashboardOutlined 图标 -->
							<svg viewBox="64 64 896 896" focusable="false" data-icon="dashboard" width="1em" height="1em" fill="currentColor" aria-hidden="true">
								<path d="M924.8 385.6a446.7 446.7 0 00-96-142.4 446.7 446.7 0 00-142.4-96C631.1 123.8 572.5 112 512 112s-119.1 11.8-174.4 35.2a446.7 446.7 0 00-142.4 96 446.7 446.7 0 00-96 142.4C75.8 440.9 64 499.5 64 560c0 132.7 58.3 257.7 159.9 343.1l1.7 1.4c5.8 4.8 13.1 7.5 20.6 7.5h531.7c7.5 0 14.8-2.7 20.6-7.5l1.7-1.4C901.7 817.7 960 692.7 960 560c0-60.5-11.9-119.1-35.2-174.4zM761.4 836H262.6A371.12 371.12 0 01140 560c0-99.4 38.7-192.8 109-263 70.3-70.3 163.7-109 263-109 99.4 0 192.8 38.7 263 109 70.3 70.3 109 163.7 109 263 0 105.6-44.5 205.5-122.6 276zM623.5 421.5a8.03 8.03 0 00-11.3 0L527.7 506c-18.7-5-39.4-.2-54.1 14.5a55.95 55.95 0 000 79.2 55.95 55.95 0 0079.2 0 55.87 55.87 0 0014.5-54.1l84.5-84.5c3.1-3.1 3.1-8.2 0-11.3l-28.3-28.3zM490 320h44c4.4 0 8-3.6 8-8v-80c0-4.4-3.6-8-8-8h-44c-4.4 0-8 3.6-8 8v80c0 4.4 3.6 8 8 8zm260 218v44c0 4.4 3.6 8 8 8h80c4.4 0 8-3.6 8-8v-44c0-4.4-3.6-8-8-8h-80c-4.4 0-8 3.6-8 8zm12.7-197.2l-31.1-31.1a8.03 8.03 0 00-11.3 0l-56.6 56.6a8.03 8.03 0 000 11.3l31.1 31.1c3.1 3.1 8.2 3.1 11.3 0l56.6-56.6c3.1-3.1 3.1-8.2 0-11.3zm-458.6-31.1a8.03 8.03 0 00-11.3 0l-31.1 31.1c-3.1 3.1-3.1 8.2 0 11.3l56.6 56.6c3.1 3.1 8.2 3.1 11.3 0l31.1-31.1c3.1-3.1 3.1-8.2 0-11.3l-56.6-56.6zM262 530h-80c-4.4 0-8 3.6-8 8v44c0 4.4 3.6 8 8 8h80c4.4 0 8-3.6 8-8v-44c0-4.4-3.6-8-8-8z"></path>
							</svg>
						</div>
						<div class="stats-content">
							<span>测点数量</span>
							<p><em>${dhNum || 0}</em><i>个</i></p>
						</div>
					</li>
				</ul>
			`;

				// 添加到容器
				container.append(listHtml);
			} else {
				console.warn('%c响应格式不符合预期，无法更新资源统计UI', 'background: #FF9800; color: white; padding: 2px 4px; border-radius: 2px;');
			}
		}

		// 更新机房列表UI的函数
		function updateEnvironmentListUI(response) {
			if (response && response.success && response.result && Array.isArray(response.result)) {
				console.log('%c更新机房列表UI', 'background: #4CAF50; color: white; padding: 4px 8px; border-radius: 4px;');

				// 获取机房列表容器
				const container = $('#environmentList');

				// 清空加载指示器
				container.empty();

				// 如果没有数据，显示提示信息
				if (response.result.length === 0) {
					container.html('<div class="loading-indicator">暂无动环数据</div>');
					return;
				}

				// 遍历动环数据
				response.result.forEach(function (item) {
					// 创建动环项HTML，只保留roomName字段，确保格式一致且不会溢出
					const roomName = item.roomName || '未命名机房';
					const itemHtml = `
					<div class="environment-item">
						<div class="environment-name" title="${roomName}">${roomName}</div>
					</div>
					`;

					// 添加到容器
					container.append(itemHtml);
				});
			} else {
				console.warn('%c响应格式不符合预期，无法更新机房列表UI', 'background: #FF9800; color: white; padding: 2px 4px; border-radius: 2px;');
				// 显示错误信息
				$('#environmentList').html('<div class="loading-indicator">获取动环数据失败</div>');
			}
		}

		// 页面加载完成后执行API调用
		document.addEventListener('DOMContentLoaded', function () {
			console.log('%c页面DOMContentLoaded事件触发', 'background: #E91E63; color: white; padding: 4px 8px; border-radius: 4px;');

			// 延迟执行API调用，确保所有资源都已加载
			setTimeout(function () {
				console.log('%c延迟执行API调用', 'background: #E91E63; color: white; padding: 4px 8px; border-radius: 4px;');
				loadPortalApiData();
			}, 1000);
		});

		// window.onload事件，确保所有资源都加载完成
		window.onload = function () {
			console.log('%cwindow.onload事件触发', 'background: #E91E63; color: white; padding: 4px 8px; border-radius: 4px;');

			// 延迟执行API调用，确保所有资源都已加载
			setTimeout(function () {
				console.log('%c延迟执行API调用', 'background: #E91E63; color: white; padding: 4px 8px; border-radius: 4px;');
				loadPortalApiData();
			}, 500);
		};
	</script>
</body>

</html>