import { defHttp } from '/@/utils/http/axios';
import { ApiResponse } from '/@/api/model/baseModel';

/**
 * 设备接口参数类型
 */
export interface DeviceParams {
  pageNo?: number;
  pageSize?: number;
  deviceId?: number;
  name?: string;
  modelName?: string;
  floorInfo?: number;
  roomName?: string;
  lengthInfo?: number;
  high?: number;
  wide?: number;
  deviceType?: string;
  weight?: number;
  temperature?: number;
  createTime?: string;
  updateTime?: string;
}

/**
 * 设备数据类型
 */
export interface DeviceModel {
  deviceId: number;
  name: string;
  modelName: string;
  floorInfo: number;
  roomName: string;
  lengthInfo: number;
  high: number;
  wide: number;
  deviceType: string;
  weight: number;
  temperature: number;
  createTime: string;
  updateTime: string;
}

/**
 * 设备列表响应类型
 */
export interface DeviceListResult {
  records: DeviceModel[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

/**
 * 设备API接口
 */
enum Api {
  DEVICE_LIST = '/device',
  DEVICE_GET = '/device/get',
  DEVICE_ADD = '/device',
  DEVICE_DELETE = '/device',
}

/**
 * 获取设备列表
 * @param params 查询参数
 * @returns 设备列表数据
 */
export function getDeviceList(params: DeviceParams) {
  return defHttp.get<ApiResponse<DeviceListResult>>({
    url: Api.DEVICE_LIST,
    params,
  });
}

/**
 * 获取单个设备详情
 * @param deviceId 设备ID
 * @returns 设备详情数据
 */
export function getDeviceById(deviceId: number) {
  return defHttp.get<ApiResponse<DeviceModel>>({
    url: Api.DEVICE_GET,
    params: { deviceId },
  });
}

/**
 * 添加设备
 * @param data 设备数据
 * @returns 添加结果
 */
export function addDevice(data: DeviceModel) {
  return defHttp.post<ApiResponse<any>>({
    url: Api.DEVICE_ADD,
    data,
  });
}

/**
 * 删除设备
 * @param idList 设备ID列表
 * @returns 删除结果
 */
export function deleteDevice(idList: number[]) {
  return defHttp.delete<ApiResponse<any>>({
    url: Api.DEVICE_DELETE,
    params: { idList: idList.join(',') },
  });
}
