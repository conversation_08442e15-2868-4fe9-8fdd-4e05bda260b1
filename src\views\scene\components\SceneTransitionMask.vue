<template>
  <Transition name="transition-mask">
    <div
      v-if="shouldShow"
      class="fixed inset-0 bg-black/60 z-[999] flex items-center justify-center pointer-events-auto select-none backdrop-blur-sm"
    >
      <div class="text-center text-white flex flex-col items-center gap-4">
        <!-- 转换进度指示器 -->
        <div class="relative w-24 h-24">
          <!-- 外圈进度环 -->
          <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
            <!-- 背景圆环 -->
            <circle cx="50" cy="50" r="45" stroke="rgba(255,255,255,0.1)" stroke-width="8" fill="none" />
            <!-- 进度圆环 -->
            <circle
              cx="50"
              cy="50"
              r="45"
              stroke="url(#progressGradient)"
              stroke-width="8"
              fill="none"
              stroke-linecap="round"
              :stroke-dasharray="circumference"
              :stroke-dashoffset="progressOffset"
              class="transition-all duration-300 ease-out"
            />
            <!-- 渐变定义 -->
            <defs>
              <linearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" style="stop-color: #3b82f6; stop-opacity: 1" />
                <stop offset="100%" style="stop-color: #06b6d4; stop-opacity: 1" />
              </linearGradient>
            </defs>
          </svg>

          <!-- 中心图标 -->
          <div class="absolute inset-0 flex items-center justify-center">
            <div class="w-8 h-8 text-blue-400">
              <svg v-if="transitionType === 'to-interior'" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2L2 7v10c0 5.55 3.84 10 9 11 1.66-.34 3.22-1.1 4.5-2.2V7l-3.5-2-3.5 2v8.5c-1.5-.5-3-1.5-4-3V7l7-3.5z" />
              </svg>
              <svg v-else viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2L2 7v10c0 5.55 3.84 10 9 11 5.16-1 9-5.45 9-11V7l-10-5z" />
              </svg>
            </div>
          </div>
        </div>

        <!-- 转换状态文本 -->
        <div class="text-lg font-medium tracking-wide">
          {{ transitionText }}
        </div>

        <!-- 进度百分比 -->
        <div class="text-sm text-blue-300"> {{ Math.round(progress) }}% </div>

        <!-- 转换阶段指示器 -->
        <div class="flex gap-2 mt-2">
          <div
            v-for="(stage, index) in transitionStages"
            :key="index"
            class="w-2 h-2 rounded-full transition-all duration-300"
            :class="getStageClass(index)"
          ></div>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
  import { computed, ref, watch } from 'vue';
  import { useGlobalThreeStore } from '../store/globalThreeStore';

  const store = useGlobalThreeStore();

  // 计算属性
  const shouldShow = computed(() => store.shouldShowTransitionMask);
  const progress = computed(() => store.sceneTransitionState.transitionProgress);
  const targetView = computed(() => store.sceneTransitionState.targetView);

  // 转换类型
  const transitionType = computed(() => {
    if (targetView.value === 2) return 'to-interior';
    if (targetView.value === 1) return 'to-exterior';
    return 'unknown';
  });

  // 转换文本
  const transitionText = computed(() => {
    const progressValue = progress.value;

    if (progressValue < 30) {
      return transitionType.value === 'to-interior' ? '准备进入内景...' : '准备切换外景...';
    } else if (progressValue < 70) {
      return transitionType.value === 'to-interior' ? '加载楼层模型...' : '加载建筑模型...';
    } else if (progressValue < 100) {
      return '应用场景设置...';
    } else {
      return '转换完成';
    }
  });

  // 进度环计算
  const circumference = 2 * Math.PI * 45; // r=45
  const progressOffset = computed(() => {
    const progressValue = Math.min(100, Math.max(0, progress.value));
    return circumference - (progressValue / 100) * circumference;
  });

  // 转换阶段
  const transitionStages = ['预处理', '模型加载', '场景设置', '完成'];

  const getStageClass = (index: number) => {
    const progressValue = progress.value;
    const stageProgress = (index + 1) * 25; // 每个阶段25%

    if (progressValue >= stageProgress) {
      return 'bg-blue-400';
    } else if (progressValue >= stageProgress - 25) {
      return 'bg-blue-400/50 animate-pulse';
    } else {
      return 'bg-white/20';
    }
  };

  // 监听转换状态变化，提供用户反馈
  watch(shouldShow, (newValue) => {
    if (newValue) {
      console.log('[SceneTransitionMask] 显示转换遮罩');
    } else {
      console.log('[SceneTransitionMask] 隐藏转换遮罩');
    }
  });

  watch(progress, (newValue) => {
    console.log(`[SceneTransitionMask] 转换进度: ${newValue}%`);
  });
</script>

<style scoped>
  .transition-mask-enter-active,
  .transition-mask-leave-active {
    transition: all 0.3s ease-out;
  }

  .transition-mask-enter-from,
  .transition-mask-leave-to {
    opacity: 0;
    backdrop-filter: blur(0px);
  }

  .transition-mask-enter-to,
  .transition-mask-leave-from {
    opacity: 1;
    backdrop-filter: blur(4px);
  }

  /* 自定义滚动条样式 */
  ::-webkit-scrollbar {
    display: none;
  }

  /* 确保遮罩层在最顶层 */
  .z-\[999\] {
    z-index: 999;
  }
</style>
