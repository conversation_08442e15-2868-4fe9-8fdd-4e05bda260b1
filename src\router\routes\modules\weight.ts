import type { AppRouteModule } from '/@/router/types';
import { LAYOUT } from '/@/router/constant';

const weight: AppRouteModule = {
  path: '/weight',
  name: 'Weight',
  component: LAYOUT,
  redirect: '/weight/index',
  meta: {
    orderNo: 45,
    icon: 'ant-design:dashboard-outlined',
    title: '楼层重量',
    hideChildrenInMenu: true,
  },
  children: [
    {
      path: 'index',
      name: 'WeightIndex',
      component: () => import('/@/views/weight/index.vue'),
      meta: {
        title: '楼层重量',
        icon: 'ant-design:dashboard-outlined',
        hideMenu: true,
      },
    },
  ],
};

export default weight;
