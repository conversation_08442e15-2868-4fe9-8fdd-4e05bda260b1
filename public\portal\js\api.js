/**
 * 子应用 API 请求工具
 * 用于向后端发送带有 token 认证的请求
 */

// API 基础路径
// 使用主应用的API前缀，通常是 '/jeecgboot'
const API_BASE_URL = '/jeecgboot';

// 检查是否在开发环境
const isDev = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
console.log('%c当前环境:', 'background: #FF9800; color: white; padding: 2px 4px; border-radius: 2px;', isDev ? '开发环境' : '生产环境');

// API缓存对象，用于存储API调用结果
const apiCache = {
  // 缓存数据
  cache: {},

  // 缓存有效期（毫秒）
  cacheExpiration: 60000, // 默认1分钟

  // 正在进行中的请求
  pendingRequests: {},

  // 设置缓存
  set: function(key, data) {
    this.cache[key] = {
      data: data,
      timestamp: Date.now()
    };
    console.log(`%c缓存API数据: ${key}`, 'background: #9C27B0; color: white; padding: 2px 4px; border-radius: 2px;');
  },

  // 获取缓存
  get: function(key) {
    const cachedItem = this.cache[key];
    if (!cachedItem) {
      return null;
    }

    // 检查缓存是否过期
    if (Date.now() - cachedItem.timestamp > this.cacheExpiration) {
      console.log(`%c缓存已过期: ${key}`, 'background: #FF9800; color: white; padding: 2px 4px; border-radius: 2px;');
      delete this.cache[key];
      return null;
    }

    console.log(`%c使用缓存数据: ${key}`, 'background: #4CAF50; color: white; padding: 2px 4px; border-radius: 2px;');
    return cachedItem.data;
  },

  // 清除缓存
  clear: function(key) {
    if (key) {
      delete this.cache[key];
      console.log(`%c清除缓存: ${key}`, 'background: #F44336; color: white; padding: 2px 4px; border-radius: 2px;');
    } else {
      this.cache = {};
      console.log('%c清除所有缓存', 'background: #F44336; color: white; padding: 2px 4px; border-radius: 2px;');
    }
  },

  // 设置正在进行中的请求
  setPendingRequest: function(key, promise) {
    this.pendingRequests[key] = promise;
  },

  // 获取正在进行中的请求
  getPendingRequest: function(key) {
    return this.pendingRequests[key];
  },

  // 清除正在进行中的请求
  clearPendingRequest: function(key) {
    delete this.pendingRequests[key];
  }
};

/**
 * 尝试多个API路径，直到成功或全部失败
 * @param {Array} paths - API路径数组
 * @param {string} apiName - API名称，用于日志和模拟数据
 * @param {number} index - 当前尝试的路径索引
 * @returns {Promise} 请求结果
 */
function tryApiPaths(paths, apiName, index = 0) {
  // 构建缓存键
  const cacheKey = `${apiName}_paths_attempt`;

  // 检查是否有缓存的成功路径
  const cachedPath = apiCache.get(cacheKey);
  if (cachedPath) {
    console.log(`%c使用缓存的成功路径: ${cachedPath}`, 'background: #4CAF50; color: white; padding: 2px 4px; border-radius: 2px;');
    // 使用缓存的成功路径
    return apiGet(cachedPath);
  }

  // 如果已经尝试了所有路径，返回模拟数据
  if (index >= paths.length) {
    console.error(`%c所有${apiName}的API路径都失败了`, 'background: #F44336; color: white; padding: 4px 8px; border-radius: 4px;');
    return Promise.resolve(createMockResponse(paths[0]));
  }

  const currentPath = paths[index];
  console.log(`%c尝试${apiName}的API路径 (${index + 1}/${paths.length}): ${currentPath}`, 'background: #FF9800; color: white; padding: 2px 4px; border-radius: 2px;');

  // 禁用缓存，因为我们正在测试不同的路径
  return apiGet(currentPath, {}, false)
    .then(function(response) {
      // 检查响应是否有效
      if (response && typeof response === 'object' && !response.toString().includes('<!DOCTYPE html>')) {
        console.log(`%c${apiName}的API路径 ${currentPath} 成功`, 'background: #4CAF50; color: white; padding: 2px 4px; border-radius: 2px;');

        // 缓存成功的路径
        apiCache.set(cacheKey, currentPath);

        return response;
      } else {
        console.warn(`%c${apiName}的API路径 ${currentPath} 返回无效响应，尝试下一个路径`, 'background: #FF9800; color: white; padding: 2px 4px; border-radius: 2px;');
        // 尝试下一个路径
        return tryApiPaths(paths, apiName, index + 1);
      }
    })
    .catch(function(error) {
      console.error(`%c${apiName}的API路径 ${currentPath} 失败: ${error.status || 'unknown'}`, 'background: #F44336; color: white; padding: 2px 4px; border-radius: 2px;');
      // 尝试下一个路径
      return tryApiPaths(paths, apiName, index + 1);
    });
}

/**
 * 创建模拟响应数据
 * @param {string} url - 请求URL
 * @returns {Object} 模拟响应数据
 */
function createMockResponse(url) {
  console.log('%c创建模拟响应数据:', 'background: #FF9800; color: white; padding: 2px 4px; border-radius: 2px;', url);

  // 根据URL返回不同的模拟数据
  if (url.includes('/rooms/typeInfo')) {
    // 测点数占比数据
    return {
      success: true,
      message: "模拟数据 - 操作成功",
      code: 200,
      result: [
        { type: 1, number: 3245 },
        { type: 2, number: 1876 },
        { type: 3, number: 1532 },
        { type: 4, number: 1124 },
        { type: 5, number: 880 }
      ],
      timestamp: new Date().getTime()
    };
  } else if (url.includes('/rooms') && !url.includes('/list') && !url.includes('/typeInfo')) {
    // 动环资源总览数据
    return {
      success: true,
      message: "模拟数据 - 操作成功",
      code: 200,
      result: {
        onlineDevices: 182,
        alarmDevices: 5,
        deviceUtilization: 89
      },
      timestamp: new Date().getTime()
    };
  } else if (url.includes('/rooms/list')) {
    // 机房列表数据
    return {
      success: true,
      message: "模拟数据 - 操作成功",
      code: 200,
      result: [
        {
          id: 1,
          name: "主机房A区",
          status: "normal",
          deviceCount: 45,
          temperature: 23.5,
          humidity: 45
        },
        {
          id: 2,
          name: "主机房B区",
          status: "normal",
          deviceCount: 38,
          temperature: 22.8,
          humidity: 42
        },
        {
          id: 3,
          name: "备用机房",
          status: "warning",
          deviceCount: 12,
          temperature: 25.2,
          humidity: 48
        }
      ],
      timestamp: new Date().getTime()
    };
  } else {
    // 默认模拟数据
    return {
      success: true,
      message: "模拟数据 - 操作成功",
      code: 200,
      result: {},
      timestamp: new Date().getTime()
    };
  }
}

/**
 * 获取认证 token
 * @returns {string} 认证 token
 */
function getAuthToken() {
  console.log('%c开始获取认证token', 'background: #FF9800; color: white; padding: 2px 4px; border-radius: 2px;');

  // 优先从全局变量获取
  if (window.portalToken) {
    console.log('%c从全局变量获取token成功', 'background: #4CAF50; color: white; padding: 2px 4px; border-radius: 2px;');
    console.log('Token值: ' + window.portalToken.substring(0, 10) + '...' + window.portalToken.substring(window.portalToken.length - 10));
    return window.portalToken;
  } else {
    console.log('%c全局变量中没有token', 'background: #FF9800; color: white; padding: 2px 4px; border-radius: 2px;');
    console.log('window.portalToken:', window.portalToken);
  }

  // 其次从 localStorage 获取
  const token = localStorage.getItem('portal_token');
  if (token) {
    console.log('%c从localStorage获取token成功', 'background: #2196F3; color: white; padding: 2px 4px; border-radius: 2px;');
    console.log('Token值: ' + token.substring(0, 10) + '...' + token.substring(token.length - 10));
    window.portalToken = token; // 同步到全局变量
    return token;
  } else {
    console.log('%cLocalStorage中没有token', 'background: #FF9800; color: white; padding: 2px 4px; border-radius: 2px;');
  }

  // 最后尝试从 wujie 框架获取
  console.log('%c尝试从wujie框架获取token', 'background: #FF9800; color: white; padding: 2px 4px; border-radius: 2px;');
  console.log('window.$wujie:', window.$wujie);

  if (window.$wujie && window.$wujie.props && window.$wujie.props.data) {
    console.log('window.$wujie.props.data:', window.$wujie.props.data);
    const wujieToken = window.$wujie.props.data.token;
    if (wujieToken) {
      console.log('%c从wujie框架获取token成功', 'background: #9C27B0; color: white; padding: 2px 4px; border-radius: 2px;');
      console.log('Token值: ' + wujieToken.substring(0, 10) + '...' + wujieToken.substring(wujieToken.length - 10));
      // 存储到全局变量和 localStorage
      window.portalToken = wujieToken;
      localStorage.setItem('portal_token', wujieToken);
      return wujieToken;
    } else {
      console.log('%cwujie框架中没有token', 'background: #FF9800; color: white; padding: 2px 4px; border-radius: 2px;');
    }
  } else {
    console.log('%c未找到wujie框架', 'background: #FF9800; color: white; padding: 2px 4px; border-radius: 2px;');
  }

  console.warn('%c未能获取到认证token', 'background: #F44336; color: white; padding: 4px 8px; border-radius: 4px;');

  // 为了测试，返回一个模拟的token
  const mockToken = 'mock_token_for_testing_' + new Date().getTime();
  console.log('%c使用模拟token用于测试', 'background: #FF9800; color: white; padding: 2px 4px; border-radius: 2px;', mockToken);
  window.portalToken = mockToken;
  localStorage.setItem('portal_token', mockToken);
  return mockToken;
}

/**
 * 发送 GET 请求
 * @param {string} url - 请求路径
 * @param {Object} params - 请求参数
 * @param {boolean} useCache - 是否使用缓存，默认为true
 * @returns {Promise} 请求结果
 */
function apiGet(url, params = {}, useCache = true) {
  // 构建完整 URL
  let fullUrl = `${API_BASE_URL}${url}`;

  // 添加查询参数
  if (Object.keys(params).length > 0) {
    const queryString = Object.keys(params)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&');
    fullUrl += `?${queryString}`;
  }

  // 构建缓存键
  const cacheKey = fullUrl;

  // 如果使用缓存，检查是否有缓存数据
  if (useCache) {
    // 检查是否有缓存数据
    const cachedData = apiCache.get(cacheKey);
    if (cachedData) {
      return Promise.resolve(cachedData);
    }

    // 检查是否有正在进行中的相同请求
    const pendingRequest = apiCache.getPendingRequest(cacheKey);
    if (pendingRequest) {
      console.log(`%c复用正在进行中的请求: ${fullUrl}`, 'background: #9C27B0; color: white; padding: 2px 4px; border-radius: 2px;');
      return pendingRequest;
    }
  }

  // 获取 token
  const token = getAuthToken();

  // 发送请求
  console.log(`%c准备发送GET请求: ${fullUrl}`, 'background: #3F51B5; color: white; padding: 2px 4px; border-radius: 2px;');
  console.log(`%c使用Token: ${token ? token.substring(0, 10) + '...' : '未获取到token'}`, 'background: #009688; color: white; padding: 2px 4px; border-radius: 2px;');

  // 检查jQuery是否可用
  if (typeof $ === 'undefined') {
    console.error('%cjQuery未加载，无法发送AJAX请求', 'background: #F44336; color: white; padding: 4px 8px; border-radius: 4px;');
    return Promise.reject(new Error('jQuery未加载，无法发送AJAX请求'));
  }

  // 创建请求Promise
  const requestPromise = new Promise((resolve, reject) => {
    try {
      console.log(`%c开始发送AJAX请求: ${fullUrl}`, 'background: #FF9800; color: white; padding: 2px 4px; border-radius: 2px;');

      $.ajax({
        url: fullUrl,
        type: 'GET',
        headers: {
          'X-Access-Token': token,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        },
        success: function(response) {
          console.log(`%cAPI请求成功: ${url}`, 'background: #4CAF50; color: white; padding: 2px 4px; border-radius: 2px;');
          console.log('响应数据:', response);

          // 检查响应格式
          if (typeof response === 'string') {
            console.warn('%c响应是字符串格式，尝试解析为JSON', 'background: #FF9800; color: white; padding: 2px 4px; border-radius: 2px;');
            try {
              response = JSON.parse(response);
              console.log('%c解析后的JSON数据:', 'background: #4CAF50; color: white; padding: 2px 4px; border-radius: 2px;', response);
            } catch (e) {
              console.error('%c响应不是有效的JSON格式', 'background: #F44336; color: white; padding: 2px 4px; border-radius: 2px;');
              // 检查是否是HTML响应
              if (response.includes('<!DOCTYPE html>') || response.includes('<html>')) {
                console.error('%c响应是HTML页面，可能是认证问题或API路径错误', 'background: #F44336; color: white; padding: 2px 4px; border-radius: 2px;');
                // 返回模拟数据
                response = createMockResponse(url);
              }
            }
          }

          // 检查响应状态
          if (response && response.code && response.code !== 200) {
            console.warn(`%c响应状态码异常: ${response.code}`, 'background: #FF9800; color: white; padding: 2px 4px; border-radius: 2px;');
            console.warn('错误消息:', response.message || '未知错误');
          }

          // 如果使用缓存，将结果存入缓存
          if (useCache && response) {
            apiCache.set(cacheKey, response);
          }

          resolve(response);
        },
        error: function(error) {
          console.error(`%cAPI请求失败: ${url}`, 'background: #F44336; color: white; padding: 2px 4px; border-radius: 2px;');
          console.error('错误信息:', error);
          console.error('状态码:', error.status);
          console.error('状态文本:', error.statusText);
          console.error('响应文本:', error.responseText);

          // 检查是否是认证问题
          if (error.status === 401 || error.status === 403) {
            console.error('%c认证失败，可能是token无效或已过期', 'background: #F44336; color: white; padding: 2px 4px; border-radius: 2px;');
          }

          // 检查是否是跨域问题
          if (error.status === 0 && error.statusText === 'error') {
            console.error('%c可能是跨域问题，检查CORS配置', 'background: #F44336; color: white; padding: 2px 4px; border-radius: 2px;');
          }

          reject(error);
        },
        complete: function() {
          console.log(`%cAPI请求完成: ${url}`, 'background: #607D8B; color: white; padding: 2px 4px; border-radius: 2px;');
          // 清除正在进行中的请求
          if (useCache) {
            apiCache.clearPendingRequest(cacheKey);
          }
        }
      });
    } catch (error) {
      console.error('%c发送AJAX请求时出错:', 'background: #F44336; color: white; padding: 4px 8px; border-radius: 4px;', error);
      // 清除正在进行中的请求
      if (useCache) {
        apiCache.clearPendingRequest(cacheKey);
      }
      reject(error);
    }
  });

  // 如果使用缓存，将请求添加到正在进行中的请求
  if (useCache) {
    apiCache.setPendingRequest(cacheKey, requestPromise);
  }

  return requestPromise;
}

/**
 * 发送 POST 请求
 * @param {string} url - 请求路径
 * @param {Object} data - 请求数据
 * @param {boolean} useCache - 是否使用缓存，默认为false（POST请求通常不缓存）
 * @returns {Promise} 请求结果
 */
function apiPost(url, data = {}, useCache = false) {
  // 构建完整 URL
  const fullUrl = `${API_BASE_URL}${url}`;

  // 构建缓存键（包含URL和请求数据）
  const cacheKey = `${fullUrl}_${JSON.stringify(data)}`;

  // 如果使用缓存，检查是否有缓存数据
  if (useCache) {
    // 检查是否有缓存数据
    const cachedData = apiCache.get(cacheKey);
    if (cachedData) {
      return Promise.resolve(cachedData);
    }

    // 检查是否有正在进行中的相同请求
    const pendingRequest = apiCache.getPendingRequest(cacheKey);
    if (pendingRequest) {
      console.log(`%c复用正在进行中的请求: ${fullUrl}`, 'background: #9C27B0; color: white; padding: 2px 4px; border-radius: 2px;');
      return pendingRequest;
    }
  }

  // 获取 token
  const token = getAuthToken();

  // 发送请求
  console.log(`%c发送POST请求: ${fullUrl}`, 'background: #3F51B5; color: white; padding: 2px 4px; border-radius: 2px;');
  console.log(`%c使用Token: ${token ? token.substring(0, 10) + '...' : '未获取到token'}`, 'background: #009688; color: white; padding: 2px 4px; border-radius: 2px;');
  console.log('请求数据:', data);

  // 创建请求Promise
  const requestPromise = new Promise((resolve, reject) => {
    try {
      $.ajax({
        url: fullUrl,
        type: 'POST',
        headers: {
          'X-Access-Token': token,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        },
        data: JSON.stringify(data),
        success: function(response) {
          console.log(`%cAPI请求成功: ${url}`, 'background: #4CAF50; color: white; padding: 2px 4px; border-radius: 2px;');
          console.log('响应数据:', response);

          // 检查响应格式
          if (typeof response === 'string') {
            console.warn('%c响应是字符串格式，尝试解析为JSON', 'background: #FF9800; color: white; padding: 2px 4px; border-radius: 2px;');
            try {
              response = JSON.parse(response);
              console.log('%c解析后的JSON数据:', 'background: #4CAF50; color: white; padding: 2px 4px; border-radius: 2px;', response);
            } catch (e) {
              console.error('%c响应不是有效的JSON格式', 'background: #F44336; color: white; padding: 2px 4px; border-radius: 2px;');
            }
          }

          // 如果使用缓存，将结果存入缓存
          if (useCache && response) {
            apiCache.set(cacheKey, response);
          }

          resolve(response);
        },
        error: function(error) {
          console.error(`%cAPI请求失败: ${url}`, 'background: #F44336; color: white; padding: 2px 4px; border-radius: 2px;');
          console.error('错误信息:', error);
          console.error('状态码:', error.status);
          console.error('状态文本:', error.statusText);
          console.error('响应文本:', error.responseText);

          reject(error);
        },
        complete: function() {
          console.log(`%cAPI请求完成: ${url}`, 'background: #607D8B; color: white; padding: 2px 4px; border-radius: 2px;');
          // 清除正在进行中的请求
          if (useCache) {
            apiCache.clearPendingRequest(cacheKey);
          }
        }
      });
    } catch (error) {
      console.error('%c发送AJAX请求时出错:', 'background: #F44336; color: white; padding: 4px 8px; border-radius: 4px;', error);
      // 清除正在进行中的请求
      if (useCache) {
        apiCache.clearPendingRequest(cacheKey);
      }
      reject(error);
    }
  });

  // 如果使用缓存，将请求添加到正在进行中的请求
  if (useCache) {
    apiCache.setPendingRequest(cacheKey, requestPromise);
  }

  return requestPromise;
}

/**
 * 示例：获取实时数据
 * @param {Object} params - 请求参数，支持分页参数 pageNo 和 pageSize
 * @returns {Promise} 请求结果
 */
function getRealTimeData(params = {}) {
  // 设置默认分页参数，避免一次性获取过多数据
  const defaultParams = {
    pageNo: 1,
    pageSize: 100, // 默认每页100条
    ...params
  };
  
  // 如果传入了分页参数，使用分页接口
  if (defaultParams.pageNo || defaultParams.pageSize) {
    // 使用分页版本的接口
    return apiGet('/cmcc/realTimeData/page', defaultParams);
  } else {
    // 保持向后兼容，如果明确不需要分页参数则使用原接口
    return apiGet('/cmcc/realTimeData', defaultParams);
  }
}

/**
 * 获取全量实时数据（不分页）
 * 注意：此函数可能返回大量数据，请谨慎使用
 * 建议仅在需要客户端分页或特殊场景下使用
 * @param {Object} params - 请求参数
 * @returns {Promise} 请求结果
 */
function getRealTimeDataNoPagination(params = {}) {
  return apiGet('/cmcc/realTimeData', params);
}

/**
 * 示例：获取设备数据
 * @param {string} code - 设备编码
 * @returns {Promise} 请求结果
 */
function getDeviceData(code) {
  return apiGet('/baDeviceData/code', { code });
}

/**
 * 获取动环左上角资源总览数据
 * @returns {Promise} 请求结果
 */
function getRooms() {
  console.log('%c调用getRooms API', 'background: #3F51B5; color: white; padding: 2px 4px; border-radius: 2px;');

  // 尝试多个可能的API路径
  const apiPaths = [
    '/api/rooms',            // 根据API文档，这是正确的路径
    '/cmcc/rooms',           // 备选路径1
    '/sys/rooms',            // 备选路径2
    '/rooms'                 // 备选路径3
  ];

  // 使用第一个路径
  const apiPath = apiPaths[0];
  console.log('%c使用API路径:', 'background: #3F51B5; color: white; padding: 2px 4px; border-radius: 2px;', apiPath);

  // 尝试所有可能的路径
  return tryApiPaths(apiPaths, 'getRooms');
}

/**
 * 获取机房列表数据
 * @returns {Promise} 请求结果
 */
function getRoomsList() {
  console.log('%c调用getRoomsList API', 'background: #3F51B5; color: white; padding: 2px 4px; border-radius: 2px;');

  // 尝试多个可能的API路径
  const apiPaths = [
    '/api/rooms/list',       // 根据API文档，这是正确的路径
    '/cmcc/rooms/list',      // 备选路径1
    '/sys/rooms/list',       // 备选路径2
    '/rooms/list'            // 备选路径3
  ];

  // 使用第一个路径
  const apiPath = apiPaths[0];
  console.log('%c使用API路径:', 'background: #3F51B5; color: white; padding: 2px 4px; border-radius: 2px;', apiPath);

  // 尝试所有可能的路径
  return tryApiPaths(apiPaths, 'getRoomsList');
}

/**
 * 获取机房列表数据
 * @returns {Promise} 请求结果
 */
function getEnvironmentList() {
  console.log('%c调用getEnvironmentList API', 'background: #3F51B5; color: white; padding: 2px 4px; border-radius: 2px;');

  // 尝试多个可能的API路径
  const apiPaths = [
    '/api/rooms/list',       // 根据需求文档指定的路径
    '/cmcc/rooms/list',      // 备选路径1
    '/rooms/list',           // 备选路径2
    '/environment/list'      // 备选路径3
  ];

  // 使用第一个路径
  const apiPath = apiPaths[0];
  console.log('%c使用API路径:', 'background: #3F51B5; color: white; padding: 2px 4px; border-radius: 2px;', apiPath);

  // 尝试所有可能的路径
  return tryApiPaths(apiPaths, 'getEnvironmentList');
}

/**
 * 获取测点数占比数据
 * @returns {Promise} 请求结果
 */
function getDeviceTypeInfo() {
  console.log('%c调用getDeviceTypeInfo API', 'background: #3F51B5; color: white; padding: 2px 4px; border-radius: 2px;');

  // 尝试多个可能的API路径
  const apiPaths = [
    '/api/rooms/typeInfo',       // 根据需求文档指定的路径
    '/cmcc/rooms/typeInfo',      // 备选路径1
    '/sys/rooms/typeInfo',       // 备选路径2
    '/rooms/typeInfo'            // 备选路径3
  ];

  // 使用第一个路径
  const apiPath = apiPaths[0];
  console.log('%c使用API路径:', 'background: #3F51B5; color: white; padding: 2px 4px; border-radius: 2px;', apiPath);

  // 尝试所有可能的路径
  return tryApiPaths(apiPaths, 'getDeviceTypeInfo');
}

/**
 * 获取动环告警数据
 * @param {Object} params - 请求参数，包含 pageNo（页码）和 pageSize（每页条数）
 * @returns {Promise} 请求结果
 */
function getAlarmData(params = {}) {
  console.log('%c调用getAlarmData API', 'background: #3F51B5; color: white; padding: 2px 4px; border-radius: 2px;');

  // 确保参数中包含分页信息
  const pageParams = {
    pageNo: params.pageNo || 1,
    pageSize: params.pageSize || 10,
    ...params
  };

  // 直接调用API，不使用tryApiPaths，因为我们确定路径
  return apiGet('/cmcc/alarmData', pageParams, false).catch(error => {
    console.error('%c获取动环告警数据失败:', 'background: #F44336; color: white; padding: 4px 8px; border-radius: 4px;', error);

    // 返回模拟数据作为备用
    return {
      success: true,
      message: "模拟数据 - 操作成功",
      code: 200,
      result: {
        records: [
          {
            id: 1,
            deviceName: "UPS-01",
            alarmLevel: "1",
            alarmDesc: "UPS电池电量低",
            alarmTime: "2023-05-15 10:30:22",
            deviceId: "DEV001",
            signalId: "SIG001",
            signalNumber: "001",
            alarmFlag: 1,
            eventValue: "20%",
            createDate: "2023-05-15 10:30:22"
          },
          {
            id: 2,
            deviceName: "空调-02",
            alarmLevel: "2",
            alarmDesc: "空调温度过高",
            alarmTime: "2023-05-15 11:15:43",
            deviceId: "DEV002",
            signalId: "SIG002",
            signalNumber: "002",
            alarmFlag: 1,
            eventValue: "28℃",
            createDate: "2023-05-15 11:15:43"
          },
          {
            id: 3,
            deviceName: "配电柜-01",
            alarmLevel: "1",
            alarmDesc: "配电柜过载",
            alarmTime: "2023-05-15 09:45:10",
            deviceId: "DEV003",
            signalId: "SIG003",
            signalNumber: "003",
            alarmFlag: 1,
            eventValue: "95%",
            createDate: "2023-05-15 09:45:10"
          }
        ],
        total: 3,
        size: pageParams.pageSize,
        current: pageParams.pageNo,
        pages: 1
      },
      timestamp: new Date().getTime()
    };
  });
}

// 导出 API 函数
window.portalApi = {
  get: apiGet,
  post: apiPost,
  getRealTimeData,
  getRealTimeDataNoPagination,
  getDeviceData,
  getRooms,
  // getRoomsList 已移除，使用 getEnvironmentList 替代
  getEnvironmentList,
  getDeviceTypeInfo,
  getAlarmData,
  baseUrl: API_BASE_URL
};
