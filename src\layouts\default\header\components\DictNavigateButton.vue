<template>
  <div class="p-1 cursor-pointer">
    <a-dropdown :trigger="['click']" overlay-class-name="navigation-dropdown">
      <div>
        <a-tooltip>
          <template #title>快速导航</template>
          <Icon icon="ant-design:compass-outlined" />
        </a-tooltip>
      </div>
      <template #overlay>
        <a-menu>
          <template v-if="navigationItems.length > 0">
            <a-menu-item v-for="item in navigationItems" :key="item.value" @click="navigateToUrl(item)">
              <span>{{ item.text || item.label || item.value }}</span>
            </a-menu-item>
          </template>
          <template v-else>
            <a-menu-item :disabled="true">
              <span>{{ isLoading ? '加载中...' : '暂无导航项' }}</span>
            </a-menu-item>
          </template>
        </a-menu>
      </template>
    </a-dropdown>
  </div>
</template>

<script setup lang="ts">
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getDictItemsByCode, ajaxGetDictItems } from '/@/utils/dict/index';
  import { Icon } from '/@/components/Icon';
  import { onMounted, ref } from 'vue';
  import { useUserStore } from '/@/store/modules/user';

  const { createMessage } = useMessage();
  const userStore = useUserStore();
  const isLoading = ref(false);
  const navigationItems = ref<any[]>([]);

  // 组件挂载时预加载字典数据
  onMounted(async () => {
    try {
      await loadNavigationItems();
    } catch (error) {
      console.error('初始化导航字典数据失败:', error);
    }
  });

  async function loadNavigationItems() {
    if (isLoading.value) return;

    try {
      isLoading.value = true;

      // 首先尝试从缓存获取
      userStore.setAllDictItemsByLocal();
      let items = getDictItemsByCode('navigationPath');

      if (!items || !Array.isArray(items) || items.length === 0) {
        // 如果缓存中没有，则从服务器获取
        console.log('从服务器获取navigationPath字典数据');

        const response = await ajaxGetDictItems('navigationPath', {});

        if (response && Array.isArray(response) && response.length > 0) {
          // 更新用户store中的字典数据
          const currentDictItems = userStore.getAllDictItems || {};
          (currentDictItems as any)['navigationPath'] = response;
          userStore.setAllDictItems(currentDictItems);
          items = response;
          console.log('成功从服务器获取字典数据:', items);
        } else {
          console.warn('服务器返回的字典数据为空或格式不正确:', response);
        }
      } else {
        console.log('从缓存获取到字典数据:', items);
      }

      // 过滤有效的导航项
      navigationItems.value = (items || []).filter((item) => item && item.value);

      return items;
    } catch (error) {
      console.error('加载字典数据失败:', error);
      navigationItems.value = [];

      if (error && typeof error === 'object' && 'message' in error) {
        const errorMessage = (error as any).message || '';
        if (errorMessage.includes('X-TIMESTAMP') || errorMessage.includes('签名验证失败')) {
          createMessage.warning('数据加载失败，请刷新页面重试');
        }
      }
    } finally {
      isLoading.value = false;
    }
  }

  /**
   * 在新标签页中打开URL
   */
  function navigateToUrl(item: any) {
    try {
      if (!item || !item.value) {
        createMessage.warning('导航地址无效');
        return;
      }

      let url = item.value.trim();

      // 确保URL格式正确
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        url = 'http://' + url;
      }

      // 在新标签页打开URL
      window.open(url, '_blank');
      console.log('已在新标签页打开:', url);
    } catch (error) {
      console.error('打开链接失败:', error);
      createMessage.error('打开链接失败，请检查URL格式');
    }
  }
</script>

<style lang="less">
  .navigation-dropdown {
    .ant-dropdown-menu {
      max-height: 400px;
      overflow-y: auto;
    }

    .ant-menu-item {
      min-width: 160px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
</style>
