import type { AppRouteModule } from '/@/router/types';
import { LAYOUT } from '/@/router/constant';

const AssetRoute: AppRouteModule = {
  path: '/asset',
  name: 'Asset',
  component: LAYOUT,
  meta: {
    orderNo: 31,
    icon: 'ant-design:gold-outlined',
    title: '资产管理',
  },
  children: [
    {
      path: 'room',
      name: 'Asset<PERSON><PERSON>',
      component: () => import('/@/views/asset/room/index.vue'),
      meta: {
        title: '房间管理',
      },
    },
    {
      path: 'device',
      name: 'AssetDevice',
      component: () => import('/@/views/asset/device/index.vue'),
      meta: {
        title: '设备管理',
      },
    },
    {
      path: 'cabinet',
      name: 'AssetCabinet',
      component: () => import('/@/views/asset/cabinet/index.vue'),
      meta: {
        title: '机柜管理',
      },
    },
  ],
};

export default AssetRoute;
