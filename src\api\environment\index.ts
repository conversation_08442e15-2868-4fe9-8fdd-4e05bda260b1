import { defHttp } from '/@/utils/http/axios';

// 从scene/alarm.ts导入类型定义
export interface AlarmParams {
  pageNo: number;
  pageSize: number;
}

export interface AlarmRecord {
  id: number;
  deviceName: string;
  alarmLevel: string;
  alarmDesc: string;
  alarmTime: string;
  deviceId: string;
  signalId: string;
  signalNumber: string;
  alarmFlag: number;
  eventValue: string;
  createDate: string;
  scId: string;
  serialNo: string;
  siteId: string;
}

export interface PageResult {
  records: AlarmRecord[];
  total: number;
  size: number;
  current: number;
  pages: number;
  orders: any[];
  optimizeCountSql: boolean;
  searchCount: boolean;
  maxLimit: number | null;
  countId: number | null;
}

enum Api {
  ALARM_LIST = '/environment/alarm/list',
  ALARM_PROCESS = '/environment/alarm/process',
  REALTIME_DATA = '/environment/realtime/list',
  /**
   * 实时数据接口 - 不分页版本
   * 适用于3D场景中展示少量实时数据
   * 不支持分页，返回所有符合条件的数据
   */
  REAL_TIME_DATA = '/cmcc/realTimeData',
  /**
   * 实时数据接口 - 分页版本
   * 适用于后台管理系统表格展示
   * 支持分页参数：pageNo, pageSize
   */
  REAL_TIME_DATA_PAGE = '/cmcc/realTimeData/page',
  /**
   * 告警数据接口
   * 从scene/alarm.ts移动过来
   */
  ALARM_DATA = '/cmcc/alarmData',
}

/**
 * 获取告警列表数据
 * @param params 查询参数
 * @returns 告警数据列表
 */
export function getAlarmList(params: any) {
  return defHttp.get({
    url: Api.REAL_TIME_DATA_PAGE,
    params: { ...params, type: 0 },
  });
}

/**
 * 处理告警
 * @param data 包含告警ID的对象
 * @returns 处理结果
 */
export function processAlarm(data: { ids: (string | number)[] }) {
  return defHttp.post({ url: Api.ALARM_PROCESS, data });
}

/**
 * 获取实时数据（不分页）
 * 此接口适合在3D场景中使用，返回所有符合条件的数据
 * 查询类型包括：
 * - type=3: 模拟输入量(遥测)
 * - type=4: 数字输入量(遥信)
 * - type=5: 设备
 * @param params 查询参数
 * @returns 实时数据列表
 */
export function getRealtimeDataNoPage(params: any) {
  return defHttp.get({
    url: Api.REAL_TIME_DATA,
    params: { ...params, type: '3' },
  });
}

/**
 * 获取实时数据（分页版本）
 * 此接口适合在后台管理系统表格中使用，支持分页
 * 查询类型包括：
 * - type=3: 模拟输入量(遥测)
 * - type=4: 数字输入量(遥信)
 * - type=5: 设备
 * @param params 查询参数，包含pageNo和pageSize
 * @returns 分页的实时数据列表
 */
export function getRealtimeData(params: any) {
  return defHttp.get({
    url: Api.REAL_TIME_DATA_PAGE,
    params: { ...params, type: '3' },
  });
}

/**
 * 获取CMCC告警数据列表
 * 从scene/alarm.ts移动过来的接口
 * @param params 查询参数，包含pageNo和pageSize
 * @returns 分页的告警数据列表
 */
export function getCmccAlarmList(params: AlarmParams) {
  return defHttp.get<PageResult>({
    url: Api.ALARM_DATA,
    params,
  });
}
