declare module 'event-source-polyfill' {
  export class EventSourcePolyfill extends EventTarget {
    constructor(url: string, configuration?: EventSourceInitDict);
    readonly CLOSED: number;
    readonly CONNECTING: number;
    readonly OPEN: number;
    readonly readyState: number;
    readonly url: string;
    readonly withCredentials: boolean;
    close(): void;
    addEventListener(type: string, listener: EventListener): void;
    removeEventListener(type: string, listener: EventListener): void;
  }

  interface EventSourceInitDict {
    withCredentials?: boolean;
    headers?: object;
    [key: string]: any;
  }
}
