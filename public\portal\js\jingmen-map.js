/**
 * 荆门市地图展示
 * 用于在实时监测页面中显示荆门市地图
 */

// 初始化地图
function initJingmenMap(containerId) {
    console.log('%c初始化荆门市地图', 'background: #4CAF50; color: white; padding: 4px 8px; border-radius: 4px;');
    console.log('容器ID:', containerId);

    // 获取容器元素
    const container = document.getElementById(containerId);
    if (!container) {
        console.error('%c找不到容器元素:', 'background: #F44336; color: white; padding: 4px 8px; border-radius: 4px;', containerId);
        return;
    }

    try {
        // 创建地图实例
        const chart = echarts.init(container);

        // 显示加载中提示
        chart.showLoading({
            text: '荆门市地图数据加载中...',
            color: '#0580f2',
            textColor: '#fff',
            maskColor: 'rgba(0, 0, 0, 0.2)'
        });

        // 加载荆门市地图数据（使用本地文件）
        $.get('./map.json', function(geoJson) {
            // 隐藏加载提示
            chart.hideLoading();

            // 注册地图
            echarts.registerMap('jingmen', geoJson);

            // 配置项
            const option = {
                backgroundColor: 'transparent',
                title: {
                    text: '荆门市地图',
                    left: 'center',
                    textStyle: {
                        color: '#fff',
                        fontSize: 16
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: '{b}'
                },
                visualMap: {
                    show: false,
                    min: 0,
                    max: 100,
                    inRange: {
                        color: ['#0a2dae', '#0044e5', '#0580f2']
                    }
                },
                series: [{
                    name: '荆门市',
                    type: 'map',
                    map: 'jingmen',
                    roam: false, // 禁用缩放和平移
                    zoom: 1.2,    // 放大地图
                    selectedMode: false, // 禁用选择
                    label: {
                        show: true,
                        color: '#fff',
                        fontSize: 12
                    },
                    emphasis: {
                        label: {
                            color: '#fff',
                            fontSize: 14,
                            fontWeight: 'bold'
                        },
                        itemStyle: {
                            areaColor: '#0066ff'
                        }
                    },
                    itemStyle: {
                        areaColor: '#0580f2',
                        borderColor: '#fff',
                        borderWidth: 1
                    },
                    data: [
                        {name: '东宝区', value: 95},
                        {name: '掇刀区', value: 90},
                        {name: '京山市', value: 85},
                        {name: '钟祥市', value: 80},
                        {name: '沙洋县', value: 75}
                    ]
                }]
            };

            // 设置配置项并渲染地图
            chart.setOption(option);

            console.log('%c荆门市地图渲染完成', 'background: #4CAF50; color: white; padding: 4px 8px; border-radius: 4px;');

            // 添加窗口大小变化事件监听
            window.addEventListener('resize', function() {
                chart.resize();
            });
        }).fail(function(error) {
            console.error('%c加载荆门市地图数据失败:', 'background: #F44336; color: white; padding: 4px 8px; border-radius: 4px;', error);

            // 隐藏加载提示
            chart.hideLoading();

            // 显示错误消息
            container.innerHTML = '<div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: #ff5252; font-size: 14px; text-align: center;">加载荆门市地图数据失败，请检查map.json文件是否存在</div>';
        });

        // 返回chart实例，以便外部可以访问
        return chart;
    } catch (error) {
        console.error('%c初始化荆门市地图时出错:', 'background: #F44336; color: white; padding: 4px 8px; border-radius: 4px;', error);

        // 显示错误消息
        container.innerHTML = '<div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: #ff5252; font-size: 14px; text-align: center;">初始化地图失败: ' + error.message + '</div>';
    }
}

// 导出函数
window.jingmenMap = {
    init: initJingmenMap
};
