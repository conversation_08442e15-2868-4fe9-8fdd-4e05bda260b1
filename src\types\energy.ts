// 电表实时数据接口
export interface ElectricityRealtimeData {
  deviceName: string;
  valueData: string;
}

// 电表历史数据接口
export interface ElectricityHistoryRecord {
  dataTime: string;
  valueData: string;
}

export interface ElectricityHistoryData {
  [meterName: string]: ElectricityHistoryRecord[];
}

// ECharts 相关类型
export interface ChartDataItem {
  name: string;
  value: number;
}

// ECharts tooltip 参数类型
export interface TooltipParams {
  name: string;
  marker: string;
  seriesName: string;
  value: number;
}
