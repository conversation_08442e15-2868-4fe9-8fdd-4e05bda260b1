<template>
  <div class="fixed top-4 right-4 z-[1000] bg-black/80 backdrop-blur-md rounded-lg p-4 text-white min-w-[300px]">
    <h3 class="text-lg font-bold mb-4 text-center">场景转换测试面板</h3>

    <!-- 场景转换测试 -->
    <div class="mb-6">
      <h4 class="text-sm font-medium mb-2 text-blue-300">大场景切换（内景/外景）</h4>
      <div class="flex gap-2 mb-2">
        <button
          @click="testSceneTransition"
          :disabled="isAnyTransitioning"
          class="flex-1 px-3 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed rounded text-sm transition-colors"
        >
          {{ store.currentView === 1 ? '切换到内景' : '切换到外景' }}
        </button>
      </div>
      <div class="text-xs text-gray-400">
        当前视图: {{ store.currentView === 1 ? '外景' : '内景' }}
        {{ store.sceneTransitionState.isTransitioning ? `(转换中: ${store.sceneTransitionState.transitionProgress}%)` : '' }}
      </div>
    </div>

    <!-- 楼层切换测试 -->
    <div class="mb-6">
      <h4 class="text-sm font-medium mb-2 text-green-300">小场景切换（楼层切换）</h4>
      <div class="grid grid-cols-2 gap-2 mb-2">
        <button
          v-for="floor in testFloors"
          :key="floor.id"
          @click="testFloorTransition(floor.id)"
          :disabled="isAnyTransitioning || store.currentFloorId === floor.id"
          class="px-3 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed rounded text-sm transition-colors"
          :class="{ 'bg-green-800': store.currentFloorId === floor.id }"
        >
          {{ floor.name }}
        </button>
      </div>
      <div class="text-xs text-gray-400">
        当前楼层: {{ currentFloorName }}
        {{ store.floorTransitionState.isTransitioning ? '(切换中...)' : '' }}
      </div>
    </div>

    <!-- 状态信息 -->
    <div class="border-t border-gray-600 pt-4">
      <h4 class="text-sm font-medium mb-2 text-yellow-300">转换状态</h4>
      <div class="space-y-1 text-xs">
        <div class="flex justify-between">
          <span>场景转换:</span>
          <span :class="store.sceneTransitionState.isTransitioning ? 'text-blue-400' : 'text-gray-400'">
            {{ store.sceneTransitionState.isTransitioning ? '进行中' : '空闲' }}
          </span>
        </div>
        <div class="flex justify-between">
          <span>楼层切换:</span>
          <span :class="store.floorTransitionState.isTransitioning ? 'text-green-400' : 'text-gray-400'">
            {{ store.floorTransitionState.isTransitioning ? '进行中' : '空闲' }}
          </span>
        </div>
        <div class="flex justify-between">
          <span>用户交互:</span>
          <span :class="store.canUserInteract ? 'text-green-400' : 'text-red-400'">
            {{ store.canUserInteract ? '允许' : '禁止' }}
          </span>
        </div>
      </div>
    </div>

    <!-- 关闭按钮 -->
    <div class="mt-4 text-center">
      <button @click="$emit('close')" class="px-4 py-2 bg-red-600 hover:bg-red-700 rounded text-sm transition-colors"> 关闭测试面板 </button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import { useGlobalThreeStore } from '../store/globalThreeStore';
  import { buildingData } from '@/data/buildingData';

  defineEmits<{
    close: [];
  }>();

  const store = useGlobalThreeStore();

  // 测试用的楼层数据
  const testFloors = computed(() => {
    return buildingData.floors?.slice(0, 4) || []; // 只显示前4个楼层
  });

  // 当前楼层名称
  const currentFloorName = computed(() => {
    if (!store.currentFloorId) return '无';
    const floor = testFloors.value.find((f) => f.id === store.currentFloorId);
    return floor?.name || store.currentFloorId;
  });

  // 是否有任何转换正在进行
  const isAnyTransitioning = computed(() => {
    return store.sceneTransitionState.isTransitioning || store.floorTransitionState.isTransitioning;
  });

  // 测试场景转换
  const testSceneTransition = async () => {
    try {
      console.log('[TransitionTestPanel] 开始测试场景转换');
      await store.toggleView();
      console.log('[TransitionTestPanel] 场景转换测试完成');
    } catch (error) {
      console.error('[TransitionTestPanel] 场景转换测试失败:', error);
    }
  };

  // 测试楼层切换
  const testFloorTransition = async (floorId: string) => {
    try {
      console.log(`[TransitionTestPanel] 开始测试楼层切换: ${floorId}`);

      // 如果当前不在内景，先切换到内景
      if (store.currentView === 1) {
        console.log('[TransitionTestPanel] 先切换到内景');
        await store.toggleView();
        // 等待场景转换完成
        await new Promise((resolve) => {
          const checkComplete = () => {
            if (!store.sceneTransitionState.isTransitioning) {
              resolve(void 0);
            } else {
              setTimeout(checkComplete, 100);
            }
          };
          checkComplete();
        });
      }

      // 然后切换楼层
      await store.switchFloor(floorId);
      console.log(`[TransitionTestPanel] 楼层切换测试完成: ${floorId}`);
    } catch (error) {
      console.error(`[TransitionTestPanel] 楼层切换测试失败: ${floorId}`, error);
    }
  };
</script>

<style scoped>
  /* 确保按钮在禁用状态下有正确的样式 */
  button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  /* 当前选中的楼层按钮样式 */
  .bg-green-800 {
    background-color: rgb(22 101 52);
  }

  /* 滚动条样式 */
  ::-webkit-scrollbar {
    width: 4px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
  }
</style>
