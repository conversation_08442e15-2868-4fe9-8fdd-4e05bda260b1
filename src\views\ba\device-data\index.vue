<template>
  <div class="p-4">
    <div class="bg-white p-4 rounded-lg shadow-sm">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-medium">设备数据</h2>
        <a-button @click="handleRefresh" :loading="loading">
          <template #icon>
            <ReloadOutlined />
          </template>
          刷新
        </a-button>
      </div>

      <!-- 数据表格 -->
      <a-table
        :columns="columns"
        :data-source="tableData"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
        size="middle"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'dataTime'">
            {{ formatTime(record.dataTime) }}
          </template>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import { message } from 'ant-design-vue';
  import { ReloadOutlined } from '@ant-design/icons-vue';
  import { getDeviceDataList, type DeviceDataItem, type DeviceDataParams } from '/@/api/ba/device-data';
  import { formatToDateTime } from '/@/utils/dateUtil';

  // 响应式数据
  const loading = ref(false);
  const tableData = ref<DeviceDataItem[]>([]);

  // 分页配置
  const pagination = reactive({
    current: 1,
    pageSize: 20,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number) => `共 ${total} 条记录`,
  });

  // 表格列配置
  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      width: 250,
    },
    {
      title: '时间',
      dataIndex: 'dataTime',
      key: 'dataTime',
      width: 180,
    },
    {
      title: '值',
      dataIndex: 'valueData',
      key: 'valueData',
      width: 150,
    },
  ];

  // 格式化时间
  const formatTime = (time: Date | string) => {
    return formatToDateTime(time);
  };

  // 构建查询参数
  const buildParams = (): DeviceDataParams => {
    return {
      pageNo: pagination.current,
      pageSize: pagination.pageSize,
    };
  };

  // 加载数据
  const loadData = async () => {
    try {
      loading.value = true;
      const params = buildParams();

      const result = await getDeviceDataList(params);
      tableData.value = result.records;
      pagination.total = result.total;
    } catch (error) {
      console.error('加载设备数据失败:', error);
      message.error('加载数据失败');
    } finally {
      loading.value = false;
    }
  };

  // 刷新
  const handleRefresh = () => {
    loadData();
    message.success('数据已刷新');
  };

  // 表格变化
  const handleTableChange = (pag: any) => {
    pagination.current = pag.current;
    pagination.pageSize = pag.pageSize;
    loadData();
  };

  // 挂载时加载数据
  onMounted(() => {
    loadData();
  });
</script>
