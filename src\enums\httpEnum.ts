/**
 * @description: Request result set
 */
export enum ResultEnum {
  SUCCESS = 0,
  ERROR = 1,
  TIMEOUT = 401,
  TYPE = 'success',
}

/**
 * @description: request method
 */
export enum RequestEnum {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  DELETE = 'DELETE',
}

/**
 * @description:  contentTyp
 */
export enum ContentTypeEnum {
  // json
  JSON = 'application/json;charset=UTF-8',
  // form-data qs
  FORM_URLENCODED = 'application/x-www-form-urlencoded;charset=UTF-8',
  // form-data  upload
  FORM_DATA = 'multipart/form-data;charset=UTF-8',
}

/**
 * 请求header
 * @description:  contentTyp
 */
export enum ConfigEnum {
  // TOKEN
  TOKEN = 'X-Access-Token',
  // TIMESTAMP
  TIMESTAMP = 'X-TIMESTAMP',
  // Sign
  Sign = 'X-Sign',
  // 租户id
  TENANT_ID = 'X-Tenant-Id',
  // 版本
  VERSION = 'X-Version',
  // 低代码应用ID
  X_LOW_APP_ID = 'X-Low-App-ID',
}

// 定义AI模型配置接口
export interface XAIConfig {
  name: string;
  API_URL: string;
  API_KEY: string;
  MODEL: string;
  logo: string;
  options?: Record<string, any>;
  isCustom?: boolean;
}

// AI模型配置项
export const AI_MODELS: Record<string, XAIConfig> = {};

// 保存自定义模型到本地存储
export function saveCustomModel(modelConfig: {
  name: string;
  API_URL: string;
  API_KEY: string;
  MODEL: string;
  logo?: string;
  options?: Record<string, any>;
}): void {
  const existingModels = getCustomModels();
  const modelId = `CUSTOM_${Date.now()}`;
  const newModel: XAIConfig = {
    name: modelConfig.name,
    API_URL: modelConfig.API_URL,
    API_KEY: modelConfig.API_KEY,
    MODEL: modelConfig.MODEL,
    logo: modelConfig.logo || '🔧',
    isCustom: true,
    options: modelConfig.options,
  };

  existingModels[modelId] = newModel;
  localStorage.setItem('CUSTOM_AI_MODELS', JSON.stringify(existingModels));

  // 更新全局模型列表
  AI_MODELS[modelId] = newModel;
}

// 获取所有自定义模型
export function getCustomModels(): Record<string, XAIConfig> {
  const modelsJson = localStorage.getItem('CUSTOM_AI_MODELS');
  if (modelsJson) {
    try {
      return JSON.parse(modelsJson);
    } catch (e) {
      console.error('解析自定义模型失败', e);
    }
  }
  return {};
}

// 删除自定义模型
export function deleteCustomModel(modelId: string): void {
  const existingModels = getCustomModels();
  delete existingModels[modelId];
  localStorage.setItem('CUSTOM_AI_MODELS', JSON.stringify(existingModels));

  // 从全局模型列表中移除
  delete AI_MODELS[modelId];
}

// 初始化加载所有自定义模型
export function initializeCustomModels(): string | null {
  const customModels = getCustomModels();

  // 将所有自定义模型添加到全局模型列表
  Object.assign(AI_MODELS, customModels);

  // 返回第一个模型的ID作为默认模型
  const modelIds = Object.keys(customModels);
  return modelIds.length > 0 ? modelIds[0] : null;
}

// 保存指定模型的API_KEY到本地
export function saveModelApiKey(modelKey: string, apiKey: string): void {
  localStorage.setItem(`AI_MODEL_API_KEY_${modelKey}`, apiKey);
}

// 获取指定模型的API_KEY
export function getModelApiKey(modelKey: string): string {
  return localStorage.getItem(`AI_MODEL_API_KEY_${modelKey}`) || '';
}

// 兼容旧版本的getCustomModel函数
export function getCustomModel(): Partial<XAIConfig> | null {
  const models = getCustomModels();
  const modelIds = Object.keys(models);
  return modelIds.length > 0 ? models[modelIds[0]] : null;
}

// 初始化模型并返回默认模型ID
export const defaultModelId = initializeCustomModels();

// 默认AI模型配置（如果有自定义模型则使用第一个，否则为默认配置）
export const XAI_CONFIG: XAIConfig =
  defaultModelId && AI_MODELS[defaultModelId]
    ? AI_MODELS[defaultModelId]
    : {
        name: '请添加AI模型',
        API_URL: '',
        API_KEY: '',
        MODEL: '',
        logo: '🔧',
        isCustom: true,
      };
