import { defHttp } from '/@/utils/http/axios';

// 历史电表数据类型定义
export interface ElectricityHistoryItem {
  valueData: number;
  dataTime: string;
  type: string;
  signalName: string;
  describe: string;
  deviceName: string;
  floorInfo: string;
  room: string;
}

// 历史数据返回类型
type HistoryDataResponse = Record<string, ElectricityHistoryItem[]>;

// API 响应类型
interface ElectricityMeterResponse {
  success: boolean;
  message: string;
  code: number;
  result: HistoryDataResponse;
  timestamp: number;
}

/**
 * 获取实时数据
 */
export function getReal(floorInfo?: string) {
  return defHttp.get<ElectricityHistoryItem[]>({
    url: '/electricityMeter/getReal',
    params: floorInfo ? { floorInfo } : undefined,
  });
}

/**
 * 获取历史数据，折线图
 */
export function getHistory(floorInfo?: string) {
  return defHttp.get<HistoryDataResponse>({
    url: '/electricityMeter/getHistory',
    params: floorInfo ? { floorInfo } : undefined,
  });
}

/**
 * 获取top10
 */
export function getTop(floorInfo?: string) {
  return defHttp.get<ElectricityMeterResponse>({
    url: '/electricityMeter/top',
    params: floorInfo ? { floorInfo } : undefined,
  });
}

/**
 * 获取每层电表数据
 */
export function getFloorEl(floorInfo?: string) {
  return defHttp.get<ElectricityMeterResponse>({
    url: '/electricityMeter/getFloorEl',
    params: floorInfo ? { floorInfo } : undefined,
  });
}
