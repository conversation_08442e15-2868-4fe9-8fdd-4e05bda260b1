<template>
  <div ref="chartRef" :style="{ height, width }"></div>
</template>
<script>
  import { defineComponent, ref, reactive, watchEffect, nextTick, watch } from 'vue';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { cloneDeep } from 'lodash-es';
  export default defineComponent({
    name: 'BarChart',
    props: {
      chartData: {
        type: Array,
        default: () => [],
      },
      option: {
        type: Object,
        default: () => ({}),
      },
      width: {
        type: String,
        default: '100%',
      },
      height: {
        type: String,
        default: 'calc(100vh - 78px)',
      },
      // update-begin--author:liaozhiyang---date:20240407---for：【QQYUN-8762】首页默认及echars颜色调整
      seriesColor: {
        type: String,
        default: '#1890ff',
      },
      // update-end--author:liaozhiyang---date:20240407---for：【QQYUN-8762】首页默认及echars颜色调整
    },
    setup(props) {
      const chartRef = ref(null);
      const { setOptions, echarts } = useECharts(chartRef);
      const option = reactive({
        tooltip: {
          trigger: 'axis',
          appendToBody: true,
          axisPointer: {
            type: 'shadow',
            label: {
              show: true,
              backgroundColor: '#333',
            },
          },
        },
        xAxis: {
          type: 'category',
          data: [],
        },
        yAxis: {
          type: 'value',
        },
        series: [
          {
            name: 'bar',
            type: 'bar',
            data: [],
            color: props.seriesColor,
          },
        ],
      });

      watchEffect(() => {
        props.chartData && initCharts();
      });

      watch(
        () => props.chartData,
        () => {
          nextTick(() => {
            if (chartRef.value) {
              const options = {
                tooltip: {
                  appendToBody: true,
                  extraCssText: 'background: rgba(0,0,0,0.7); border-radius: 4px; border: none; box-shadow: 0 0 3px rgba(0, 0, 0, 0.2); color: #fff;',
                },
              };
              const mergedOptions = Object.assign({}, option, options, props.option);
              echarts.setOption(mergedOptions);
            }
          });
        },
        { deep: true, immediate: true }
      );

      function initCharts() {
        if (props.option) {
          Object.assign(option, cloneDeep(props.option));
        }
        let seriesData = props.chartData.map((item) => {
          return item.value;
        });
        let xAxisData = props.chartData.map((item) => {
          return item.name;
        });
        option.series[0].data = seriesData;
        // update-begin--author:liaozhiyang---date:20240407---for：【QQYUN-8762】首页默认及echars颜色调整
        option.series[0].color = props.seriesColor;
        // update-end--author:liaozhiyang---date:20240407---for：【QQYUN-8762】首页默认及echars颜色调整
        option.xAxis.data = xAxisData;
        setOptions(option);
      }
      return { chartRef };
    },
  });
</script>
