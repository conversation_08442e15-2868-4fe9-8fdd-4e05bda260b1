import { defHttp } from '/@/utils/http/axios';

/**
 * 房间接口参数类型
 */
export interface RoomParams {
  current?: number;
  size?: number;
  id?: number;
  roomName?: string;
  floors?: number;
  remark?: string;
}

/**
 * 房间数据类型
 */
export interface RoomModel {
  id: number;
  roomName: string;
  floors: number;
  remark: string;
}

/**
 * 房间列表响应类型
 */
export interface RoomListResult {
  records: RoomModel[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

/**
 * 房间API接口
 */
enum Api {
  ROOM_LIST = '/assetRoom',
  ROOM_GET = '/assetRoom',
  ROOM_ADD = '/assetRoom',
  ROOM_UPDATE = '/assetRoom',
  ROOM_DELETE = '/assetRoom',
}

/**
 * 获取房间列表
 * @param params 查询参数
 * @returns 房间列表数据
 */
export function getRoomList(params: RoomParams) {
  return defHttp.get<RoomListResult>({
    url: Api.ROOM_LIST,
    params,
  });
}

/**
 * 获取单个房间详情
 * @param id 房间ID
 * @returns 房间详情数据
 */
export function getRoomById(id: number) {
  return defHttp.get<RoomModel>({
    url: `${Api.ROOM_GET}/${id}`,
  });
}

/**
 * 新增房间
 * @param data 房间数据
 * @returns 新增结果
 */
export function addRoom(data: Omit<RoomModel, 'id'>) {
  return defHttp.post<any>({
    url: Api.ROOM_ADD,
    data,
  });
}

/**
 * 修改房间
 * @param data 房间数据
 * @returns 修改结果
 */
export function updateRoom(data: RoomModel) {
  return defHttp.put<any>({
    url: Api.ROOM_UPDATE,
    data,
  });
}

/**
 * 删除房间
 * @param idList 房间ID列表
 * @returns 删除结果
 */
export function deleteRoom(idList: number[]) {
  return defHttp.delete<any>({
    url: Api.ROOM_DELETE,
    params: { idList: idList.join(',') },
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  });
}
