import { defHttp } from '/@/utils/http/axios';

// 温湿度数据项
interface TemperatureItem {
  dataValue: number; // 温度/湿度值
  unit: string; // 单位
}

/**
 * 获取温度数据
 */
export function getTemperature() {
  return defHttp.get<TemperatureItem[]>({
    url: '/modbus/temperature/getTemperature',
  });
}

/**
 * 获取湿度数据
 */
export function getHumidity() {
  return defHttp.get<TemperatureItem[]>({
    url: '/modbus/temperature/getHumidity',
  });
}
