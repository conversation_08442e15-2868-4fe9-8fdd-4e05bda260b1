/**
 * MeshMerger 增强版使用示例
 * 展示如何使用新的优化功能来提升场景性能
 */

import * as THREE from 'three';
import { MeshMerger } from './MeshMerger';

/**
 * 基础使用示例
 */
export function basicEnhancedMerge(scene: THREE.Object3D) {
  const meshMerger = MeshMerger.getInstance();

  // 使用增强版合并，启用所有优化功能
  const result = meshMerger.enhancedMergeMeshes(scene, {
    enabled: true,
    maxTriangles: 50000,
    mergeDifferentMaterials: false,
    verbose: true,
    enableLOD: true,
    lodDistances: [50, 150, 300], // 50米、150米、300米处切换LOD
    enableGeometryCompression: true,
    enableFrustumCulling: true,
    minMergeDistance: 10, // 10米内的物体优先合并
    enableInstancing: true, // 启用实例化渲染
  });

  console.log(`性能优化完成: ${result.originalMeshCount} -> ${result.mergedMeshCount} meshes`);
  console.log(`预计性能提升: ${meshMerger['estimatePerformanceGain'](result)}%`);
}

/**
 * 楼层模型优化示例
 */
export function optimizeFloorModel(scene: THREE.Object3D) {
  const meshMerger = MeshMerger.getInstance();

  // 楼层模型的特殊优化配置
  const result = meshMerger.enhancedMergeMeshes(scene, {
    enabled: true,
    maxTriangles: 30000, // 楼层模型较复杂，降低单个mesh的面数
    mergeDifferentMaterials: false,
    verbose: false,
    enableLOD: true,
    lodDistances: [30, 100, 200], // 更细分的LOD距离
    enableGeometryCompression: true,
    enableFrustumCulling: true,
    minMergeDistance: 5, // 楼层内的物体距离较近
    enableInstancing: true,
  });

  return result;
}

/**
 * 大型外景模型优化示例
 */
export function optimizeExteriorModel(scene: THREE.Object3D) {
  const meshMerger = MeshMerger.getInstance();

  // 外景模型的配置，适合大范围场景
  const result = meshMerger.enhancedMergeMeshes(scene, {
    enabled: true,
    maxTriangles: 80000, // 外景可以承受更大的mesh
    mergeDifferentMaterials: true, // 外景可以适当合并不同材质
    verbose: false,
    enableLOD: true,
    lodDistances: [100, 300, 800], // 更大的LOD距离
    enableGeometryCompression: true,
    enableFrustumCulling: true,
    minMergeDistance: 20, // 外景物体间距较大
    enableInstancing: true,
  });

  return result;
}

/**
 * 性能监控和调试示例
 */
export function performanceMonitoring(scene: THREE.Object3D) {
  const meshMerger = MeshMerger.getInstance();

  console.log('=== 开始性能优化监控 ===');

  // 记录优化前的状态
  const beforeStats = {
    meshCount: 0,
    triangleCount: 0,
    renderCalls: 0,
  };

  scene.traverse((object) => {
    if (object instanceof THREE.Mesh) {
      beforeStats.meshCount++;
      beforeStats.triangleCount += meshMerger['getTriangleCount'](object.geometry);
    }
  });

  console.log('优化前状态:', beforeStats);

  // 执行增强版合并
  const startTime = performance.now();
  const result = meshMerger.enhancedMergeMeshes(scene, {
    enabled: true,
    maxTriangles: 50000,
    verbose: true,
    enableLOD: true,
    enableGeometryCompression: true,
    enableFrustumCulling: true,
    enableInstancing: true,
  });
  const endTime = performance.now();

  // 记录优化结果
  console.log('=== 优化结果统计 ===');
  console.log(`优化耗时: ${(endTime - startTime).toFixed(2)}ms`);
  console.log(`Mesh数量: ${beforeStats.meshCount} -> ${result.mergedMeshCount}`);
  console.log(`减少比例: ${((1 - result.mergedMeshCount / beforeStats.meshCount) * 100).toFixed(1)}%`);
  console.log(`预计DrawCall减少: ${beforeStats.meshCount - result.mergedMeshCount}`);
  console.log(`内存优化估算: ${result.memoryEstimate}`);

  // 获取合并器统计信息
  const stats = meshMerger.getStats();
  console.log('全局统计:', stats);

  return result;
}

/**
 * 分阶段优化示例（适合大型场景）
 */
export function phaseOptimization(scene: THREE.Object3D) {
  const meshMerger = MeshMerger.getInstance();

  console.log('=== 开始分阶段优化 ===');

  // 阶段1: 预处理 - 实例化重复几何体
  console.log('阶段1: 实例化重复几何体');
  const instanceResult = meshMerger.enhancedMergeMeshes(scene, {
    enabled: true,
    maxTriangles: 30000,
    enableInstancing: true,
    enableLOD: false,
    enableGeometryCompression: false,
    enableFrustumCulling: false,
  });

  // 阶段2: 合并相近的mesh
  console.log('阶段2: 合并相近的mesh');
  const mergeResult = meshMerger.enhancedMergeMeshes(scene, {
    enabled: true,
    maxTriangles: 50000,
    enableInstancing: false, // 已经在阶段1处理
    enableLOD: false,
    enableGeometryCompression: true,
    enableFrustumCulling: false,
    minMergeDistance: 15,
  });

  // 阶段3: 最终优化 - LOD和视锥剔除
  console.log('阶段3: 应用LOD和视锥剔除');
  const finalResult = meshMerger.enhancedMergeMeshes(scene, {
    enabled: true,
    maxTriangles: 50000,
    enableInstancing: false,
    enableLOD: true,
    enableGeometryCompression: false, // 已经在阶段2处理
    enableFrustumCulling: true,
    lodDistances: [50, 150, 400],
  });

  console.log('=== 分阶段优化完成 ===');
  return finalResult;
}

/**
 * 实时性能监控类
 */
export class MeshMergerPerformanceMonitor {
  private meshMerger: MeshMerger;
  private stats: {
    totalOptimizations: number;
    totalMeshesProcessed: number;
    totalTimeSpent: number;
    averageGain: number;
  };

  constructor() {
    this.meshMerger = MeshMerger.getInstance();
    this.stats = {
      totalOptimizations: 0,
      totalMeshesProcessed: 0,
      totalTimeSpent: 0,
      averageGain: 0,
    };
  }

  /**
   * 监控优化过程
   */
  public monitorOptimization(scene: THREE.Object3D, options: any = {}) {
    const startTime = performance.now();

    // 统计优化前的mesh数量
    let beforeMeshCount = 0;
    scene.traverse((object) => {
      if (object instanceof THREE.Mesh) {
        beforeMeshCount++;
      }
    });

    // 执行优化
    const result = this.meshMerger.enhancedMergeMeshes(scene, options);

    const endTime = performance.now();
    const timeSpent = endTime - startTime;

    // 更新统计
    this.stats.totalOptimizations++;
    this.stats.totalMeshesProcessed += beforeMeshCount;
    this.stats.totalTimeSpent += timeSpent;

    const currentGain = this.meshMerger['estimatePerformanceGain'](result);
    this.stats.averageGain = (this.stats.averageGain * (this.stats.totalOptimizations - 1) + currentGain) / this.stats.totalOptimizations;

    // 输出本次优化结果
    console.log(`[性能监控] 第${this.stats.totalOptimizations}次优化完成:`);
    console.log(`  - 处理时间: ${timeSpent.toFixed(2)}ms`);
    console.log(`  - Mesh减少: ${beforeMeshCount} -> ${result.mergedMeshCount}`);
    console.log(`  - 性能提升: ${currentGain}%`);

    return result;
  }

  /**
   * 获取总体统计
   */
  public getOverallStats() {
    return {
      ...this.stats,
      averageTimePerMesh: this.stats.totalMeshesProcessed > 0 ? this.stats.totalTimeSpent / this.stats.totalMeshesProcessed : 0,
    };
  }

  /**
   * 生成性能报告
   */
  public generatePerformanceReport(): string {
    const stats = this.getOverallStats();
    return `
=== MeshMerger 性能报告 ===
总优化次数: ${stats.totalOptimizations}
总处理Mesh数: ${stats.totalMeshesProcessed}
总耗时: ${stats.totalTimeSpent.toFixed(2)}ms
平均性能提升: ${stats.averageGain.toFixed(1)}%
平均每Mesh处理时间: ${stats.averageTimePerMesh.toFixed(3)}ms
=========================
    `.trim();
  }
}

/**
 * 自定义优化策略示例
 */
export function customOptimizationStrategy(scene: THREE.Object3D, sceneType: 'floor' | 'exterior' | 'interior') {
  const meshMerger = MeshMerger.getInstance();

  // 根据场景类型选择不同的优化策略
  const strategyConfigs = {
    floor: {
      maxTriangles: 30000,
      lodDistances: [25, 80, 200],
      minMergeDistance: 5,
      enableGeometryCompression: true,
    },
    exterior: {
      maxTriangles: 80000,
      lodDistances: [100, 300, 800],
      minMergeDistance: 20,
      enableGeometryCompression: false, // 外景保持高质量
    },
    interior: {
      maxTriangles: 40000,
      lodDistances: [20, 60, 150],
      minMergeDistance: 8,
      enableGeometryCompression: true,
    },
  };

  const config = strategyConfigs[sceneType];

  return meshMerger.enhancedMergeMeshes(scene, {
    enabled: true,
    verbose: true,
    enableLOD: true,
    enableFrustumCulling: true,
    enableInstancing: true,
    mergeDifferentMaterials: sceneType === 'exterior', // 只有外景合并不同材质
    ...config,
  });
}

/**
 * 使用示例
 */
export function runOptimizationExamples() {
  // 这里需要实际的场景对象
  // const scene = loadYourScene();

  console.log('MeshMerger 增强版优化示例:');
  console.log('1. 基础优化: basicEnhancedMerge(scene)');
  console.log('2. 楼层优化: optimizeFloorModel(scene)');
  console.log('3. 外景优化: optimizeExteriorModel(scene)');
  console.log('4. 性能监控: performanceMonitoring(scene)');
  console.log('5. 分阶段优化: phaseOptimization(scene)');
  console.log('6. 自定义策略: customOptimizationStrategy(scene, "floor")');
}
