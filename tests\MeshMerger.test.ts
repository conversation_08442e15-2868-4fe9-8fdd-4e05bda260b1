/**
 * MeshMerger 单元测试
 * 专门测试 Dengtiao mesh 处理逻辑
 */
import * as THREE from 'three';
import { MeshMerger } from './MeshMerger';

describe('MeshMerger Dengtiao 处理测试', () => {
  let meshMerger: MeshMerger;
  let scene: THREE.Scene;

  beforeEach(() => {
    meshMerger = MeshMerger.getInstance();
    scene = new THREE.Scene();
  });

  test('各种 Dengtiao 命名模式的 mesh 都应该被保留', () => {
    // 创建不同命名模式的灯条mesh
    const geometry = new THREE.BoxGeometry(1, 1, 1);
    const material = new THREE.MeshBasicMaterial({ color: 0xff0000 });

    const testCases = [
      'Waijng_Dengtiao_001',
      'Dengtiao_Light_Strip_001',
      'dengtiao_lowercase',
      'DENGTIAO_UPPERCASE',
      'Building_Dengtiao_Exterior',
    ];

    const dengtiaoMeshes = testCases.map((name) => {
      const mesh = new THREE.Mesh(geometry.clone(), material.clone());
      mesh.name = name;
      scene.add(mesh);
      return mesh;
    });

    // 执行合并
    const result = meshMerger.mergeMeshes(scene, { verbose: true });

    // 验证：所有灯条mesh都应该被保留
    const remainingMeshes = scene.children.filter((child) => child instanceof THREE.Mesh);
    expect(remainingMeshes.length).toBe(testCases.length);

    testCases.forEach((name) => {
      const meshExists = remainingMeshes.some((mesh) => mesh.name === name);
      expect(meshExists).toBe(true);
    });

    // 没有mesh被合并（因为都是灯条）
    expect(result.mergedMeshCount).toBe(0);
    expect(result.originalMeshCount).toBe(0);
  });

  test('父对象名称包含 Dengtiao 的子mesh也应该被保留', () => {
    const geometry = new THREE.BoxGeometry(1, 1, 1);
    const material = new THREE.MeshBasicMaterial({ color: 0xff0000 });

    // 创建父组对象
    const dengtiaoGroup = new THREE.Group();
    dengtiaoGroup.name = 'Waijng_Dengtiao_Group';
    scene.add(dengtiaoGroup);

    // 在父组下创建子mesh
    const childMesh = new THREE.Mesh(geometry, material);
    childMesh.name = 'Child_Light_Component';
    dengtiaoGroup.add(childMesh);

    // 执行合并
    const result = meshMerger.mergeMeshes(scene, { verbose: true });

    // 验证：子mesh应该被保留（因为父对象是灯条）
    const allMeshes: THREE.Mesh[] = [];
    scene.traverse((obj) => {
      if (obj instanceof THREE.Mesh) {
        allMeshes.push(obj);
      }
    });

    expect(allMeshes.length).toBe(1);
    expect(allMeshes[0].name).toBe('Child_Light_Component');
    expect(result.mergedMeshCount).toBe(0);
  });

  test('半透明的 Dengtiao mesh 应该被跳过（不参与分类）', () => {
    // 创建一个半透明的 Dengtiao mesh
    const geometry = new THREE.BoxGeometry(1, 1, 1);
    const material = new THREE.MeshBasicMaterial({
      color: 0xff0000,
      transparent: true,
      opacity: 0.5, // 低于阈值 0.8
    });
    const dengtiaoMesh = new THREE.Mesh(geometry, material);
    dengtiaoMesh.name = 'Waijng_Dengtiao_Semi_Transparent';

    scene.add(dengtiaoMesh);

    // 执行合并
    const result = meshMerger.mergeMeshes(scene, { verbose: true });

    // 验证：半透明mesh应该被跳过，保留在场景中
    const remainingMeshes = scene.children.filter((child) => child instanceof THREE.Mesh);
    expect(remainingMeshes.length).toBe(1);
    expect(remainingMeshes[0].name).toBe('Waijng_Dengtiao_Semi_Transparent');
    expect(result.mergedMeshCount).toBe(0);
  });

  test('多个相同材质的非设备mesh应该被合并，但Dengtiao应该被保留', () => {
    // 创建多个非设备mesh（相同材质）
    const geometry = new THREE.BoxGeometry(1, 1, 1);
    const sharedMaterial = new THREE.MeshBasicMaterial({ color: 0x00ff00 });

    const mesh1 = new THREE.Mesh(geometry.clone(), sharedMaterial.clone());
    mesh1.name = 'Wall_001';

    const mesh2 = new THREE.Mesh(geometry.clone(), sharedMaterial.clone());
    mesh2.name = 'Wall_002';

    // 创建一个Dengtiao mesh（相同材质但名字表明是灯条）
    const dengtiaoMesh = new THREE.Mesh(geometry.clone(), sharedMaterial.clone());
    dengtiaoMesh.name = 'Waijng_Dengtiao_001';

    scene.add(mesh1, mesh2, dengtiaoMesh);

    // 执行合并
    const result = meshMerger.mergeMeshes(scene, { verbose: true });

    // 验证：
    const remainingMeshes = scene.children.filter((child) => child instanceof THREE.Mesh);
    console.log(
      '剩余的meshes:',
      remainingMeshes.map((m) => m.name)
    );

    // 应该有：1个合并的mesh + 1个保留的Dengtiao mesh
    expect(remainingMeshes.length).toBeGreaterThanOrEqual(1);

    // Dengtiao应该被保留
    const dengtiaoStillExists = remainingMeshes.some((mesh) => mesh.name === 'Waijng_Dengtiao_001');
    expect(dengtiaoStillExists).toBe(true);
  });

  afterEach(() => {
    // 清理
    scene.clear();
  });
});
