<template>
  <div class="slide-wrap">
    <div class="createArea">
      <a-button type="dashed" @click="handleCreate">新建聊天</a-button>
    </div>
    <div class="historyArea">
      <ul>
        <li
          v-for="item in dataSource.history"
          :key="item.uuid"
          class="list"
          :class="[item.uuid == dataSource.active ? 'active' : 'normal', dataSource.history.length == 1 ? 'last' : '']"
          @click="handleToggleChat(item)"
        >
          <i class="icon message">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              aria-hidden="true"
              role="img"
              class="iconify iconify--ri"
              width="1em"
              height="1em"
              viewBox="0 0 24 24"
            >
              <path
                fill="currentColor"
                d="M2 8.994A5.99 5.99 0 0 1 8 3h8c3.313 0 6 2.695 6 5.994V21H8c-3.313 0-6-2.695-6-5.994zM20 19V8.994A4.004 4.004 0 0 0 16 5H8a3.99 3.99 0 0 0-4 3.994v6.012A4.004 4.004 0 0 0 8 19zm-6-8h2v2h-2zm-6 0h2v2H8z"
              />
            </svg>
          </i>
          <a-input
            class="title"
            ref="inputRef"
            v-if="editingItems[item.uuid]"
            :value="editableItems[item.uuid] || item.title"
            placeholder="请输入标题"
            @input="(e: Event) => handleInputChange(e, item.uuid)"
            @blur="inputBlur(item)"
          />
          <span class="title" v-else>{{ item.title }}</span>
          <span class="icon edit" @click="handleEdit(item)" v-if="!editingItems[item.uuid]">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              aria-hidden="true"
              role="img"
              class="iconify iconify--ri"
              width="1em"
              height="1em"
              viewBox="0 0 24 24"
            >
              <path
                fill="currentColor"
                d="M6.414 15.89L16.556 5.748l-1.414-1.414L5 14.476v1.414zm.829 2H3v-4.243L14.435 2.212a1 1 0 0 1 1.414 0l2.829 2.829a1 1 0 0 1 0 1.414zM3 19.89h18v2H3z"
              />
            </svg>
          </span>
          <span class="icon del" v-if="!editingItems[item.uuid]">
            <a-popconfirm title="确定删除此记录？" placement="bottom" ok-text="确定" cancel-text="取消" @confirm="handleDel(item)">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                aria-hidden="true"
                role="img"
                class="iconify iconify--ri"
                width="1em"
                height="1em"
                viewBox="0 0 24 24"
                @click.stop=""
              >
                <path
                  fill="currentColor"
                  d="M17 6h5v2h-2v13a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V8H2V6h5V3a1 1 0 0 1 1-1h8a1 1 0 0 1 1 1zm1 2H6v12h12zm-9 3h2v6H9zm4 0h2v6h-2zM9 4v2h6V4z"
                />
              </svg>
            </a-popconfirm>
          </span>
          <span class="icon save" v-if="editingItems[item.uuid]" @click="handleSave(item)">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              aria-hidden="true"
              role="img"
              class="iconify iconify--ri"
              width="1em"
              height="1em"
              viewBox="0 0 24 24"
            >
              <path
                fill="currentColor"
                d="M7 19v-6h10v6h2V7.828L16.172 5H5v14zM4 3h13l4 4v13a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1m5 12v4h6v-4z"
              />
            </svg>
          </span>
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, watch, nextTick } from 'vue';

  interface HistoryItem {
    uuid: number;
    title: string;
    isEdit: boolean;
  }

  interface DataSource {
    active: number;
    history: HistoryItem[];
    chat: any[];
    usingContext: boolean;
    selectedModel: string;
  }

  const props = defineProps<{
    dataSource: DataSource;
  }>();

  const emit = defineEmits<{
    (event: 'save', updatedDataSource?: DataSource): void;
    (event: 'update:dataSource', updatedDataSource: DataSource): void;
  }>();

  const inputRef = ref<HTMLInputElement[]>([]);
  const editingItems = reactive<Record<string | number, boolean>>({});
  const editableItems = reactive<Record<string | number, string>>({});

  // 新建聊天
  const handleCreate = () => {
    const uuid = getUuid();
    const newDataSource = { ...props.dataSource };

    // 修改数据的副本
    newDataSource.history = [{ title: '新建聊天', uuid, isEdit: false }, ...props.dataSource.history];
    newDataSource.chat = [{ uuid, data: [] }, ...props.dataSource.chat];

    // 新建第一个(需要高亮选中)
    if (newDataSource.history.length === 1) {
      newDataSource.active = uuid;
    }

    // 发送事件给父组件更新
    emit('update:dataSource', newDataSource);
    emit('save', newDataSource);
  };

  // 切换聊天
  const handleToggleChat = (item: HistoryItem) => {
    if (item.uuid !== props.dataSource.active) {
      const newDataSource = { ...props.dataSource, active: item.uuid };

      // 如果有正在编辑的项，先保存
      const editingItemUuid = Object.keys(editingItems).find((key) => editingItems[key]);
      if (editingItemUuid) {
        const editingItem = props.dataSource.history.find((item) => String(item.uuid) === editingItemUuid);
        if (editingItem) {
          const updatedHistory = props.dataSource.history.map((historyItem) => {
            if (historyItem.uuid === Number(editingItemUuid)) {
              return {
                ...historyItem,
                title: editableItems[editingItemUuid] || historyItem.title,
                isEdit: false,
              };
            }
            return historyItem;
          });

          newDataSource.history = updatedHistory;
        }

        // 清除编辑状态
        editingItems[editingItemUuid] = false;
      }

      emit('update:dataSource', newDataSource);
    }
  };

  // 输入变化
  const handleInputChange = (e: Event, uuid: string | number) => {
    const target = e.target as HTMLInputElement;
    editableItems[uuid] = target.value.trim();
  };

  // 失去焦点时保存
  const inputBlur = (item: HistoryItem) => {
    handleSave(item);
  };

  // 编辑
  const handleEdit = (item: HistoryItem) => {
    // 保存当前标题到临时状态
    editableItems[item.uuid] = item.title;
    // 设置为编辑模式
    editingItems[item.uuid] = true;

    // 等待DOM更新后聚焦
    nextTick(() => {
      if (inputRef.value && inputRef.value.length) {
        const input = inputRef.value.find((el) => el && el.parentElement?.querySelector('.title')?.textContent === item.title);
        input?.focus();
      }
    });
  };

  // 保存
  const handleSave = (item: HistoryItem) => {
    // 取消编辑模式
    editingItems[item.uuid] = false;

    // 如果有变更，更新数据
    if (editableItems[item.uuid] !== undefined && editableItems[item.uuid] !== item.title) {
      const newDataSource = { ...props.dataSource };
      newDataSource.history = props.dataSource.history.map((historyItem) => {
        if (historyItem.uuid === item.uuid) {
          return {
            ...historyItem,
            title: editableItems[item.uuid] || item.title,
          };
        }
        return historyItem;
      });

      emit('update:dataSource', newDataSource);
      emit('save', newDataSource);
    }
  };

  // 删除
  const handleDel = (data: HistoryItem) => {
    const findIndex = props.dataSource.history.findIndex((item) => item.uuid === data.uuid);

    if (findIndex !== -1) {
      const newDataSource = { ...props.dataSource };

      // 从历史记录和聊天数据中移除
      newDataSource.history = [...props.dataSource.history];
      newDataSource.chat = [...props.dataSource.chat];

      newDataSource.history.splice(findIndex, 1);
      newDataSource.chat.splice(findIndex, 1);

      // 处理 active 项的设置
      if (newDataSource.history.length) {
        if (props.dataSource.active === data.uuid) {
          if (findIndex > 0) {
            newDataSource.active = newDataSource.history[findIndex - 1].uuid;
          } else {
            newDataSource.active = newDataSource.history[0].uuid;
          }
        }
      } else {
        // 删除了最后一个
        newDataSource.active = null;
      }

      emit('update:dataSource', newDataSource);
      emit('save', newDataSource);
    }
  };

  // 监听输入框引用，自动聚焦
  watch(
    () => inputRef.value,
    (newVal: HTMLInputElement[]) => {
      if (newVal?.length) {
        // 延迟聚焦，确保DOM已更新
        setTimeout(() => {
          const editingId = Object.keys(editingItems).find((id) => editingItems[id]);
          if (editingId && newVal.length) {
            const inputElement = newVal.find((el) => el);
            inputElement?.focus();
          }
        }, 0);
      }
    },
    { deep: true }
  );

  // 生成唯一ID
  const getUuid = (len = 10, radix = 16): number => {
    const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
    const uuid: string[] = [];

    radix = radix || chars.length;

    if (len) {
      for (let i = 0; i < len; i++) {
        uuid[i] = chars[0 | (Math.random() * radix)];
      }
    } else {
      let r;
      uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-';
      uuid[14] = '4';
      for (let i = 0; i < 36; i++) {
        if (!uuid[i]) {
          r = 0 | (Math.random() * 16);
          uuid[i] = chars[i === 19 ? (r & 0x3) | 0x8 : r];
        }
      }
    }

    // 返回数字类型
    return parseInt(uuid.join(''), 16) % 10000000000; // 取模以避免数字过大
  };
</script>

<style scoped lang="less">
  .slide-wrap {
    border-right: 1px solid #e5e7eb;
    height: 100%;
    display: flex;
    flex-direction: column;
    .historyArea {
      padding: 20px;
      padding-top: 0;
      flex: 1;
      min-height: 0;
      overflow: auto;
      &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
    }
    .historyArea ul li:hover {
      .del {
        display: block;
      }
    }
    .createArea {
      padding: 20px;
      padding-bottom: 0;
    }
    .ant-btn {
      width: 100%;
      margin-bottom: 10px;
    }
  }
  ul {
    margin-bottom: 0;
  }
  .list {
    width: 100%;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    padding-left: 0.75rem;
    padding-right: 0.75rem;
    border-radius: 0.375rem;
    border-width: 1px;
    cursor: pointer;
    margin-bottom: 10px;
    color: #333;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    &:hover,
    &.active {
      border-color: @primary-color;
      color: @primary-color;
    }
    .edit,
    .save,
    .del {
      display: none;
    }
    &.active {
      .edit,
      .save,
      .del {
        display: block;
      }
      &.last {
        .del {
          display: none;
        }
      }
    }
    .message {
      margin-right: 8px;
    }
    .edit {
      margin-right: 8px;
    }
    .title {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      &.ant-input {
        margin-right: 20px;
      }
    }
    svg {
      vertical-align: middle;
    }
  }
</style>
