<template>
  <div class="w-full h-full p-20px">
    <div class="h-full w-full bg-white flex flex-col">
      <div class="flex-1 min-h-0">
        <div id="scrollRef" ref="scrollRef" class="overflow-y-auto h-full">
          <template v-if="chatData.length">
            <div class="p-10px">
              <chatMessage
                v-for="(item, index) of chatData"
                :key="index"
                :date-time="item.dateTime"
                :text="item.text"
                :inversion="item.inversion"
                :error="item.error"
                :loading="item.loading"
              />
            </div>
            <div v-if="loading" class="flex justify-center py-10px">
              <a-button type="primary" danger @click="handleStop" class="flex items-center justify-center">
                <svg
                  t="1706148514627"
                  class="icon mr-5px"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="5214"
                  width="18"
                  height="18"
                >
                  <path
                    d="M512 967.111111c-250.311111 0-455.111111-204.8-455.111111-455.111111s204.8-455.111111 455.111111-455.111111 455.111111 204.8 455.111111 455.111111-204.8 455.111111-455.111111 455.111111z m0-56.888889c221.866667 0 398.222222-176.355556 398.222222-398.222222s-176.355556-398.222222-398.222222-398.222222-398.222222 176.355556-398.222222 398.222222 176.355556 398.222222 398.222222 398.222222z"
                    fill="currentColor"
                    p-id="5215"
                  />
                  <path d="M341.333333 341.333333h341.333334v341.333334H341.333333z" fill="currentColor" p-id="5216" />
                </svg>
                <span>停止响应</span>
              </a-button>
            </div>
          </template>
          <template v-else>
            <div class="flex justify-center items-center text-[#d4d4d4]">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                aria-hidden="true"
                role="img"
                class="mr-2 text-3xl iconify iconify--ri"
                width="1em"
                height="1em"
                viewBox="0 0 24 24"
              >
                <path
                  fill="currentColor"
                  d="M16 16a3 3 0 1 1 0 6a3 3 0 0 1 0-6M6 12a4 4 0 1 1 0 8a4 4 0 0 1 0-8m8.5-10a5.5 5.5 0 1 1 0 11a5.5 5.5 0 0 1 0-11"
                />
              </svg>
              <span>新建聊天</span>
            </div>
          </template>
        </div>
      </div>
      <div class="flex flex-col px-16px py-6px">
        <div class="mb-6px">
          <div class="flex items-center gap-8px">
            <model-selector v-model:modelKey="selectedModelKey" @change="handleModelChange" />
            <presetQuestion :chatData="props.chatData" :loading="loading" @outQuestion="handleOutQuestion" />
          </div>
        </div>
        <div class="flex items-center">
          <a-button type="text" class="p-0 w-40px h-40px rounded-full flex items-center justify-center mr-8px" @click="handleDelSession">
            <svg
              t="1706504908534"
              class="icon"
              viewBox="0 0 1024 1024"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              p-id="1584"
              width="18"
              height="18"
            >
              <path
                d="M816.872727 158.254545h-181.527272V139.636364c0-39.563636-30.254545-69.818182-69.818182-69.818182h-107.054546c-39.563636 0-69.818182 30.254545-69.818182 69.818182v18.618181H207.127273c-48.872727 0-90.763636 41.890909-90.763637 93.09091s41.890909 90.763636 90.763637 90.763636h609.745454c51.2 0 90.763636-41.890909 90.763637-90.763636 0-51.2-41.890909-93.090909-90.763637-93.09091zM435.2 139.636364c0-13.963636 9.309091-23.272727 23.272727-23.272728h107.054546c13.963636 0 23.272727 9.309091 23.272727 23.272728v18.618181h-153.6V139.636364z m381.672727 155.927272H207.127273c-25.6 0-44.218182-20.945455-44.218182-44.218181 0-25.6 20.945455-44.218182 44.218182-44.218182h609.745454c25.6 0 44.218182 20.945455 44.218182 44.218182 0 23.272727-20.945455 44.218182-44.218182 44.218181zM835.490909 407.272727h-121.018182c-13.963636 0-23.272727 9.309091-23.272727 23.272728s9.309091 23.272727 23.272727 23.272727h97.745455V837.818182c0 39.563636-30.254545 69.818182-69.818182 69.818182h-37.236364V602.763636c0-13.963636-9.309091-23.272727-23.272727-23.272727s-23.272727 9.309091-23.272727 23.272727V907.636364h-118.690909V602.763636c0-13.963636-9.309091-23.272727-23.272728-23.272727s-23.272727 9.309091-23.272727 23.272727V907.636364H372.363636V602.763636c0-13.963636-9.309091-23.272727-23.272727-23.272727s-23.272727 9.309091-23.272727 23.272727V907.636364h-34.909091c-39.563636 0-69.818182-30.254545-69.818182-69.818182V453.818182H558.545455c13.963636 0 23.272727-9.309091 23.272727-23.272727s-9.309091-23.272727-23.272727-23.272728H197.818182c-13.963636 0-23.272727 9.309091-23.272727 23.272728V837.818182c0 65.163636 51.2 116.363636 116.363636 116.363636h451.490909c65.163636 0 116.363636-51.2 116.363636-116.363636V430.545455c0-13.963636-11.636364-23.272727-23.272727-23.272728z"
                fill="currentColor"
                p-id="1585"
              />
            </svg>
          </a-button>
          <a-textarea
            ref="inputRef"
            v-model:value="prompt"
            :autoSize="{ minRows: 1, maxRows: 6 }"
            :placeholder="placeholder"
            @pressEnter="handleEnter"
            autofocus
            class="mx-16px"
          />
          <a-button
            @click="
              () => {
                handleSubmit();
              }
            "
            :disabled="loading"
            type="primary"
            class="h-36px flex items-center text-22px px-10px"
          >
            <svg
              t="1706147858151"
              class="icon"
              viewBox="0 0 1024 1024"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              p-id="4237"
              width="1em"
              height="1em"
            >
              <path
                d="M865.28 202.5472c-17.1008-15.2576-41.0624-19.6608-62.5664-11.5712L177.7664 427.1104c-23.2448 8.8064-38.5024 29.696-39.6288 54.5792-1.1264 24.8832 11.9808 47.104 34.4064 58.0608l97.5872 47.7184c4.5056 2.2528 8.0896 6.0416 9.9328 10.6496l65.4336 161.1776c7.7824 19.1488 24.4736 32.9728 44.7488 37.0688 20.2752 4.096 41.0624-2.1504 55.6032-16.7936l36.352-36.352c6.4512-6.4512 16.5888-7.8848 24.576-3.3792l156.5696 88.8832c9.4208 5.3248 19.8656 8.0896 30.3104 8.0896 8.192 0 16.4864-1.6384 24.2688-5.0176 17.8176-7.68 30.72-22.8352 35.4304-41.6768l130.7648-527.1552c5.5296-22.016-1.7408-45.2608-18.8416-60.416z m-20.8896 50.7904L713.5232 780.4928c-1.536 6.2464-5.8368 11.3664-11.776 13.9264s-12.5952 2.1504-18.2272-1.024L526.9504 704.512c-9.4208-5.3248-19.8656-7.9872-30.208-7.9872-15.9744 0-31.744 6.144-43.52 17.92l-36.352 36.352c-3.8912 3.8912-8.9088 5.9392-14.2336 6.0416l55.6032-152.1664c0.512-1.3312 1.2288-2.56 2.2528-3.6864l240.3328-246.1696c8.2944-8.4992-2.048-21.9136-12.3904-16.0768L301.6704 559.8208c-4.096-3.584-8.704-6.656-13.6192-9.1136L190.464 502.9888c-11.264-5.5296-11.5712-16.1792-11.4688-19.3536 0.1024-3.1744 1.536-13.824 13.2096-18.2272L817.152 229.2736c10.4448-3.9936 18.0224 1.3312 20.8896 3.8912 2.8672 2.4576 9.0112 9.3184 6.3488 20.1728z"
                p-id="4238"
                fill="currentColor"
              />
            </svg>
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import type { Ref } from 'vue';
  import { computed, ref, createVNode, onUnmounted, onMounted, provide, defineComponent } from 'vue';
  import { useScroll } from '../hooks/useScroll';

  import chatMessage from './chatMessage.vue';
  import presetQuestion from './presetQuestion.vue';
  import modelSelector from './ModelSelector.vue';
  import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
  import { message, Modal } from 'ant-design-vue';
  import '../style/github-markdown.less';
  import '../style/highlight.less';
  import '../style/style.less';
  import { XAI_CONFIG, getModelApiKey, defaultModelId } from '/@/enums/httpEnum';

  // 定义聊天消息的接口
  interface ChatMessage {
    dateTime: string;
    text: string;
    inversion: boolean;
    error: boolean;
    loading: boolean;
    conversationOptions: any;
    requestOptions: any;
    thought_process?: string;
  }

  const props = withDefaults(
    defineProps<{
      chatData: ChatMessage[];
      uuid: string;
      dataSource: any;
    }>(),
    {
      chatData: () => [],
      uuid: '',
      dataSource: null,
    }
  );

  const emit = defineEmits<{
    (e: 'save'): void;
    (e: 'updateTitle', title: string): void;
    (e: 'update:chatData', data: ChatMessage[]): void;
  }>();

  const { scrollRef, scrollToBottom } = useScroll();
  const prompt = ref<string>('');
  const loading = ref<boolean>(false);
  const inputRef = ref<Ref | null>(null);

  const uuid = computed(() => {
    return props.uuid;
  });
  let evtSource: any = null;

  const conversationList = computed(() =>
    props.chatData.filter((item: { inversion: any; conversationOptions: any }) => !item.inversion && !!item.conversationOptions)
  );
  const placeholder = computed(() => {
    return '来说点什么吧...（Shift + Enter = 换行）';
  });

  const options = ref({
    parentMessageId: '',
    messages: [] as { role: string; content: string }[],
  });

  // 添加响应控制器
  const abortController = ref<AbortController | null>(null);
  // 添加reader引用以便控制中止
  const activeReader = ref<ReadableStreamDefaultReader | null>(null);

  // 添加AI模型选择
  const selectedModelKey = ref(defaultModelId || 'CUSTOM');
  const currentModel = ref(XAI_CONFIG);

  // 提供当前模型给子组件使用
  provide('currentModel', currentModel);

  const handleModelChange = (model: typeof XAI_CONFIG) => {
    currentModel.value = model;
    message.success(`已切换至 ${model.name} 模型`);
  };

  function handleEnter(event: KeyboardEvent) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSubmit();
    }
  }

  function handleSubmit() {
    let messageText = prompt.value;
    if (!messageText || messageText.trim() === '') return;
    prompt.value = '';
    onConversation(messageText);
  }

  const handleOutQuestion = (messageText: string) => {
    onConversation(messageText);
  };

  const internalChatData = computed({
    get: () => props.chatData,
    set: (val: ChatMessage[]) => {
      emit('update:chatData', val);
    },
  });

  async function onConversation(messageText: string) {
    loading.value = true;
    abortController.value = new AbortController();

    if (!currentModel.value || !currentModel.value.MODEL) {
      message.error('请先添加AI模型配置！');
      loading.value = false;
      return;
    }

    const lastContext = conversationList.value[conversationList.value.length - 1]?.conversationOptions;

    // 始终使用上下文，移除条件判断
    if (lastContext) {
      options.value.parentMessageId = lastContext.parentMessageId;
      // 保留历史消息
      if (internalChatData.value.length > 0) {
        options.value.messages = internalChatData.value.map((chat: { inversion: any; text: any }) => ({
          role: chat.inversion ? 'user' : 'assistant',
          content: chat.text,
        }));
      }
    } else {
      // 如果没有上下文，初始化messages数组
      options.value.messages = [];
      options.value.parentMessageId = '';
    }

    // 添加用户消息
    addChat(uuid.value, {
      dateTime: new Date().toLocaleString(),
      text: messageText,
      inversion: true,
      error: false,
      loading: false,
      conversationOptions: null,
      requestOptions: { prompt: messageText, options: { ...options.value } },
    });

    // 添加AI响应消息占位
    addChat(uuid.value, {
      dateTime: new Date().toLocaleString(),
      text: '',
      inversion: false,
      error: false,
      loading: true,
      conversationOptions: null,
      requestOptions: { prompt: messageText, options: { ...options.value } },
    });

    // 添加新消息到上下文
    options.value.messages.push({
      role: 'user',
      content: messageText,
    });

    try {
      const requestBody = {
        model: currentModel.value.MODEL,
        messages: options.value.messages,
        stream: true,
        ...(currentModel.value.options || {}),
      };
      // 获取API Key
      const apiKey = getModelApiKey(selectedModelKey.value) || currentModel.value.API_KEY;
      if (!apiKey) {
        message.error('请先为当前模型设置API Key！');
        loading.value = false;
        return;
      }
      const response = await fetch('/ai-api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${apiKey}`,
        },
        body: JSON.stringify(requestBody),
        signal: abortController.value.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('无法获取响应流');
      }
      activeReader.value = reader; // 保存当前reader引用
      const decoder = new TextDecoder();
      let content = '';
      let thoughtProcess = '';
      let inThoughtMode = false;

      while (true) {
        try {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              if (data === '[DONE]') continue;

              try {
                const parsed = JSON.parse(data);
                const delta = parsed.choices[0].delta.content || '';

                // 检测思考过程和最终答案的标记
                if (delta.includes('【思考过程】')) {
                  inThoughtMode = true;
                  thoughtProcess = '';
                  continue;
                }
                if (delta.includes('【最终答案】')) {
                  inThoughtMode = false;
                  content = '';
                  continue;
                }

                // 根据模式追加内容
                if (inThoughtMode) {
                  thoughtProcess += delta;
                } else {
                  content += delta;
                }

                // 实时更新聊天内容
                updateChat(uuid.value, internalChatData.value.length - 1, {
                  dateTime: new Date().toLocaleString(),
                  text: content,
                  thought_process: thoughtProcess,
                  inversion: false,
                  error: false,
                  loading: true,
                  conversationOptions: {
                    conversationId: parsed.id,
                    parentMessageId: parsed.choices[0].index,
                  },
                  requestOptions: { prompt: messageText, options: { ...options.value } },
                });
                scrollToBottom();
              } catch (e) {
                console.error('Parse error:', e);
              }
            }
          }
        } catch (error: any) {
          if (error.name === 'AbortError') {
            throw error; // 向上传递中止错误
          }
          console.error('Stream reading error:', error);
          break;
        }
      }

      // 更新最终消息状态
      updateChatSome(uuid.value, internalChatData.value.length - 1, { loading: false });

      // 在成功响应后添加assistant消息到历史记录
      options.value.messages.push({
        role: 'assistant',
        content: content,
      });

      // 如果是新对话的第一次回复,自动生成标题
      if (internalChatData.value.length <= 2) {
        await generateTitle(options.value.messages);
      }
    } catch (error: any) {
      console.error('Chat error:', error);
      // 判断是否是手动中止
      if (error.name === 'AbortError') {
        updateChat(uuid.value, internalChatData.value.length - 1, {
          dateTime: new Date().toLocaleString(),
          text: '回答已经被中止',
          inversion: false,
          error: false,
          loading: false,
          conversationOptions: null,
          requestOptions: { prompt: messageText, options: { ...options.value } },
        });
      } else {
        updateChat(uuid.value, internalChatData.value.length - 1, {
          dateTime: new Date().toLocaleString(),
          text: error.message || '请求失败',
          inversion: false,
          error: true,
          loading: false,
          conversationOptions: null,
          requestOptions: { prompt: messageText, options: { ...options.value } },
        });
      }
    } finally {
      loading.value = false;
      activeReader.value = null;
      abortController.value = null;
      scrollToBottom();
    }
  }
  async function generateTitle(messages: Array<{ role: string; content: string }>) {
    try {
      const response = await fetch('/ai-api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${currentModel.value.API_KEY}`,
        },
        body: JSON.stringify({
          model: currentModel.value.MODEL,
          messages: [
            {
              role: 'system',
              content: '请你帮我总结对话内容,生成一个简短的标题,不超过20个字。直接返回标题文本,不要其他任何修饰语。',
            },
            ...messages.slice(0, 2), // 只取前两条消息用于生成标题
          ],
          stream: false,
          ...(currentModel.value.options || {}),
        }),
      });

      if (!response.ok) {
        throw new Error('生成标题失败');
      }

      const result = await response.json();
      const title = result.choices[0].message.content.trim();
      emit('updateTitle', title);
    } catch (error) {
      console.error('生成标题错误:', error);
    }
  }
  onUnmounted(() => {
    handleStop();
  });
  const addChat = (uuid: string, data: ChatMessage) => {
    internalChatData.value.push(data);
  };
  const updateChat = (uuid: string, index: number, data: ChatMessage) => {
    internalChatData.value.splice(index, 1, data);
  };
  const updateChatSome = (uuid: string, index: number, data: Partial<ChatMessage>) => {
    internalChatData.value[index] = { ...internalChatData.value[index], ...data };
  };
  // 清空会话
  const handleDelSession = () => {
    Modal.confirm({
      title: '清空会话',
      icon: createVNode(ExclamationCircleOutlined),
      content: '是否清空会话?',
      closable: true,
      okText: '确定',
      cancelText: '取消',
      async onOk() {
        try {
          return await new Promise<void>((resolve) => {
            internalChatData.value.length = 0;
            emit('save');
            resolve();
          });
        } catch {
          return console.log('Oops errors!');
        }
      },
    });
  };
  // 停止响应
  const handleStop = async () => {
    if (loading.value) {
      loading.value = false;
    }

    // 先关闭流读取
    if (activeReader.value) {
      try {
        await activeReader.value.cancel();
      } catch (error) {
        console.error('Error canceling reader:', error);
      }
      activeReader.value = null;
    }

    // 中止请求
    if (abortController.value) {
      abortController.value.abort();
      abortController.value = null;
    }

    // 关闭 EventSource
    if (evtSource) {
      evtSource?.close();
      updateChatSome(uuid.value, internalChatData.value.length - 1, { loading: false });
    }
  };
  onMounted(() => {
    scrollToBottom();
  });
</script>

<style lang="less" scoped>
  .ai-thinking-bar {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    .dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #1890ff;
      margin-right: 6px;
      animation: ai-bounce 1.2s infinite both;
      &.dot2 {
        animation-delay: 0.2s;
      }
      &.dot3 {
        animation-delay: 0.4s;
      }
    }
    .thinking-text {
      color: #1890ff;
      font-size: 14px;
      margin-left: 6px;
      letter-spacing: 1px;
    }
  }
  @keyframes ai-bounce {
    0%,
    80%,
    100% {
      transform: scale(1);
    }
    40% {
      transform: scale(1.5);
    }
  }
  .message-text {
    padding: 8px;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
  }
</style>
