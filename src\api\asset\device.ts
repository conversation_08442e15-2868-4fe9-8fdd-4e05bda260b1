import { defHttp } from '/@/utils/http/axios';

/**
 * 资产设备接口参数类型
 */
export interface AssetDeviceParams {
  pageNo?: number;
  pageSize?: number;
  id?: number;
  type?: string;
  roomId?: number;
  building?: string;
  deviceName?: string;
  models?: string;
  code?: string;
  manufacturer?: string;
  productionDate?: string;
  enableDate?: string;
  productionNum?: string;
  deviceLong?: number;
  deviceWide?: number;
  deviceHigh?: number;
  deviceWeight?: number;
}

/**
 * 资产设备数据类型
 */
export interface AssetDeviceModel {
  id: number;
  type: string;
  roomId: number;
  building: string;
  deviceName: string;
  models: string;
  code?: string;
  manufacturer?: string;
  productionDate?: string;
  enableDate?: string;
  productionNum?: string;
  deviceLong?: number;
  deviceWide?: number;
  deviceHigh?: number;
  deviceWeight?: number;
}

/**
 * 资产设备列表响应类型
 */
export interface AssetDeviceListResult {
  records: AssetDeviceModel[];
  total: number;
  size: number;
  current: number;
  pages: number;
}

/**
 * API响应基础类型
 */
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  code: number;
  result: T;
  timestamp: number;
}

/**
 * 资产设备API接口
 */
enum Api {
  DEVICE_LIST = '/assetDevice',
  DEVICE_GET = '/assetDevice',
  DEVICE_ADD = '/assetDevice',
  DEVICE_UPDATE = '/assetDevice',
  DEVICE_DELETE = '/assetDevice',
  DEVICE_IMPORT = '/assetDevice/import',
  DEVICE_DOWNLOAD_TEMPLATE = '/assetDevice/downloadTemplate',
}

/**
 * 获取资产设备列表
 * @param params 查询参数
 * @returns 设备列表数据
 */
export function getAssetDeviceList(params: AssetDeviceParams) {
  return defHttp.get<AssetDeviceListResult>({
    url: Api.DEVICE_LIST,
    params,
  });
}

/**
 * 获取单个资产设备详情
 * @param id 设备ID
 * @returns 设备详情数据
 */
export function getAssetDeviceById(id: number) {
  return defHttp.get<AssetDeviceModel>({
    url: `${Api.DEVICE_GET}/${id}`,
  });
}

/**
 * 添加资产设备
 * @param data 设备数据
 * @returns 添加结果
 */
export function addAssetDevice(data: Omit<AssetDeviceModel, 'id'>) {
  return defHttp.post<ApiResponse>({
    url: Api.DEVICE_ADD,
    data,
  });
}

/**
 * 更新资产设备
 * @param data 设备数据
 * @returns 更新结果
 */
export function updateAssetDevice(data: AssetDeviceModel) {
  return defHttp.put<ApiResponse>({
    url: Api.DEVICE_UPDATE,
    data,
  });
}

/**
 * 删除资产设备
 * @param idList 设备ID列表
 * @returns 删除结果
 */
export function deleteAssetDevice(idList: number[]) {
  return defHttp.delete<ApiResponse>({
    url: Api.DEVICE_DELETE,
    params: { idList: idList.join(',') },
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  });
}

/**
 * 导入资产设备
 * @param file 导入文件
 * @returns 导入结果
 */
export function importAssetDevice(file: File) {
  const formData = new FormData();
  formData.append('file', file);

  return defHttp.post<ApiResponse>({
    url: Api.DEVICE_IMPORT,
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 下载资产设备导入模板
 * @returns 模板文件
 */
export function downloadAssetDeviceTemplate() {
  return defHttp.post(
    {
      url: Api.DEVICE_DOWNLOAD_TEMPLATE,
      responseType: 'blob',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    },
    { isTransformResponse: false }
  );
}

/**
 * 导出API地址
 */
export const getImportUrl = Api.DEVICE_IMPORT;
export const getDownloadTemplateUrl = Api.DEVICE_DOWNLOAD_TEMPLATE;
