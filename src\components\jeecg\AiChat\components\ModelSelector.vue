<template>
  <div class="model-selector">
    <a-dropdown :trigger="['click']">
      <a-button type="text" class="model-btn" :title="currentModel.name">
        <span class="model-logo">{{ currentModel.logo }}</span>
        <span class="model-name">{{ currentModel.name }}</span>
        <down-outlined />
      </a-button>
      <template #overlay>
        <a-menu @click="handleMenuClick">
          <a-menu-item v-for="(model, key) in AI_MODELS" :key="key" class="model-menu-item">
            <div class="model-item-content">
              <span class="model-logo">{{ model.logo }}</span>
              <span class="model-name">{{ model.name }}</span>
              <a-button type="link" size="small" @click.stop="openApiKeyModal(key)" class="action-btn">API Key</a-button>
              <a-button type="link" size="small" danger @click.stop="deleteModel(key)" class="action-btn">删除</a-button>
            </div>
          </a-menu-item>
          <a-menu-divider />
          <a-menu-item key="ADD_CUSTOM">
            <plus-outlined />
            <span>添加自定义模型</span>
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>

    <custom-model-form v-model:visible="customModelVisible" @save="handleCustomModelSave" />
    <model-api-key-form v-model:visible="apiKeyModalVisible" :modelKey="apiKeyModelKey" @save="handleApiKeySave" />
  </div>
</template>

<script lang="ts">
  import { defineComponent, ref, watch, type PropType, type SetupContext } from 'vue';
  import { DownOutlined, PlusOutlined } from '@ant-design/icons-vue';
  import { AI_MODELS, getCustomModels, deleteCustomModel, saveModelApiKey, type XAIConfig } from '/@/enums/httpEnum';
  import CustomModelForm from './CustomModelForm.vue';
  import ModelApiKeyForm from './ModelApiKeyForm.vue';

  interface Props {
    modelKey: string;
  }

  export default defineComponent({
    name: 'ModelSelector',
    components: {
      DownOutlined,
      PlusOutlined,
      CustomModelForm,
      ModelApiKeyForm,
    },
    props: {
      modelKey: {
        type: String as PropType<string>,
        default: 'CUSTOM',
      },
    },
    emits: ['update:modelKey', 'change'],
    setup(props: Props, { emit }: SetupContext) {
      const currentModel = ref<XAIConfig>(
        AI_MODELS[props.modelKey] || {
          name: '请添加AI模型',
          API_URL: '',
          API_KEY: '',
          MODEL: '',
          logo: '🔧',
          isCustom: true,
        }
      );

      const customModelVisible = ref(false);
      const apiKeyModalVisible = ref(false);
      const apiKeyModelKey = ref('');

      // 确保自定义模型加载
      getCustomModels();

      watch(
        () => props.modelKey,
        (newVal: string) => {
          currentModel.value = AI_MODELS[newVal] || currentModel.value;
        }
      );

      const handleMenuClick = (e: { key: string }) => {
        if (e.key === 'ADD_CUSTOM') {
          customModelVisible.value = true;
          return;
        }

        emit('update:modelKey', e.key);
        emit('change', AI_MODELS[e.key]);
        currentModel.value = AI_MODELS[e.key];
      };

      const handleCustomModelSave = () => {
        // 重新加载模型列表
        location.reload(); // 简单的重新加载，也可以用更优雅的方式
      };

      function openApiKeyModal(modelKey: string) {
        apiKeyModelKey.value = modelKey;
        apiKeyModalVisible.value = true;
      }

      function handleApiKeySave({ modelKey, apiKey }: { modelKey: string; apiKey: string }) {
        saveModelApiKey(modelKey, apiKey);
        apiKeyModalVisible.value = false;
      }

      function deleteModel(modelKey: string) {
        if (confirm('确定要删除这个模型配置吗？')) {
          deleteCustomModel(modelKey);
          // 如果删除的是当前选中的模型，切换到第一个可用模型
          if (props.modelKey === modelKey) {
            const remainingKeys = Object.keys(AI_MODELS);
            if (remainingKeys.length > 0) {
              emit('update:modelKey', remainingKeys[0]);
              emit('change', AI_MODELS[remainingKeys[0]]);
              currentModel.value = AI_MODELS[remainingKeys[0]];
            }
          }
        }
      }

      return {
        AI_MODELS,
        currentModel,
        customModelVisible,
        apiKeyModalVisible,
        apiKeyModelKey,
        handleMenuClick,
        handleCustomModelSave,
        openApiKeyModal,
        handleApiKeySave,
        deleteModel,
      };
    },
  });
</script>

<style lang="less" scoped>
  .model-selector {
    .model-btn {
      display: flex;
      align-items: center;
      padding: 4px 8px;
      border-radius: 4px;

      &:hover {
        background-color: rgba(0, 0, 0, 0.04);
      }

      .model-logo {
        font-size: 16px;
        margin-right: 6px;
      }

      .model-name {
        max-width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-right: 4px;
      }
    }
  }

  :deep(.ant-dropdown-menu-item) {
    padding: 8px 12px;

    .model-item-content {
      display: flex;
      align-items: center;
      width: 100%;

      .model-logo {
        font-size: 16px;
        margin-right: 8px;
      }

      .model-name {
        font-size: 14px;
        flex: 1;
        min-width: 0;
      }

      .action-btn {
        font-size: 12px;
        padding: 0 4px;
        margin-left: 4px;
      }
    }
  }
</style>
