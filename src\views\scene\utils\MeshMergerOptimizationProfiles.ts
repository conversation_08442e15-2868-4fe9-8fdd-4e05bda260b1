/**
 * MeshMerger 优化配置文件
 * 为不同场景类型和设备性能提供预设的优化配置
 */

import * as THREE from 'three';
import { MeshMerger } from './MeshMerger';

/**
 * 设备性能等级
 */
export enum DevicePerformance {
  LOW = 'low', // 低端设备（移动端、老旧电脑）
  MEDIUM = 'medium', // 中端设备（一般电脑）
  HIGH = 'high', // 高端设备（游戏电脑、工作站）
  ULTRA = 'ultra', // 超高端设备（高端工作站）
}

/**
 * 场景类型
 */
export enum SceneType {
  FLOOR_SIMPLE = 'floor_simple', // 简单楼层
  FLOOR_COMPLEX = 'floor_complex', // 复杂楼层
  BUILDING_EXTERIOR = 'building_exterior', // 建筑外观
  BUILDING_INTERIOR = 'building_interior', // 建筑内部
  LANDSCAPE = 'landscape', // 景观环境
  INDUSTRIAL = 'industrial', // 工业场景
  MIXED_COMPLEX = 'mixed_complex', // 复杂混合场景
}

/**
 * 扩展的合并选项接口
 */
interface ExtendedMergeOptions {
  enabled: boolean;
  maxTriangles: number;
  mergeDifferentMaterials: boolean;
  verbose: boolean;
  enableLOD: boolean;
  lodDistances: number[];
  enableGeometryCompression: boolean;
  enableFrustumCulling: boolean;
  minMergeDistance: number;
  enableInstancing: boolean;

  // 新增配置项
  aggressiveOptimization: boolean; // 激进优化模式
  preserveHighDetail: boolean; // 保持高细节
  batchSizeMultiplier: number; // 批次大小倍数
  compressionLevel: number; // 压缩等级 (1-5)
}

/**
 * 优化配置管理器
 */
export class MeshMergerOptimizationProfiles {
  private static instance: MeshMergerOptimizationProfiles | null = null;

  private profiles: Map<string, ExtendedMergeOptions> = new Map();

  private constructor() {
    this.initializeProfiles();
  }

  public static getInstance(): MeshMergerOptimizationProfiles {
    if (!MeshMergerOptimizationProfiles.instance) {
      MeshMergerOptimizationProfiles.instance = new MeshMergerOptimizationProfiles();
    }
    return MeshMergerOptimizationProfiles.instance;
  }

  /**
   * 初始化所有预设配置
   */
  private initializeProfiles(): void {
    // 低端设备配置
    this.createLowEndProfiles();

    // 中端设备配置
    this.createMediumEndProfiles();

    // 高端设备配置
    this.createHighEndProfiles();

    // 超高端设备配置
    this.createUltraEndProfiles();
  }

  /**
   * 创建低端设备配置
   */
  private createLowEndProfiles(): void {
    // 低端设备 - 简单楼层
    this.profiles.set(`${DevicePerformance.LOW}_${SceneType.FLOOR_SIMPLE}`, {
      enabled: true,
      maxTriangles: 15000,
      mergeDifferentMaterials: true,
      verbose: false,
      enableLOD: true,
      lodDistances: [20, 50, 100],
      enableGeometryCompression: true,
      enableFrustumCulling: true,
      minMergeDistance: 3,
      enableInstancing: true,
      aggressiveOptimization: true,
      preserveHighDetail: false,
      batchSizeMultiplier: 0.6,
      compressionLevel: 5,
    });

    // 低端设备 - 复杂楼层
    this.profiles.set(`${DevicePerformance.LOW}_${SceneType.FLOOR_COMPLEX}`, {
      enabled: true,
      maxTriangles: 12000,
      mergeDifferentMaterials: true,
      verbose: false,
      enableLOD: true,
      lodDistances: [15, 40, 80],
      enableGeometryCompression: true,
      enableFrustumCulling: true,
      minMergeDistance: 2,
      enableInstancing: true,
      aggressiveOptimization: true,
      preserveHighDetail: false,
      batchSizeMultiplier: 0.5,
      compressionLevel: 5,
    });

    // 低端设备 - 建筑外观
    this.profiles.set(`${DevicePerformance.LOW}_${SceneType.BUILDING_EXTERIOR}`, {
      enabled: true,
      maxTriangles: 25000,
      mergeDifferentMaterials: true,
      verbose: false,
      enableLOD: true,
      lodDistances: [50, 150, 400],
      enableGeometryCompression: true,
      enableFrustumCulling: true,
      minMergeDistance: 10,
      enableInstancing: true,
      aggressiveOptimization: true,
      preserveHighDetail: false,
      batchSizeMultiplier: 0.7,
      compressionLevel: 4,
    });
  }

  /**
   * 创建中端设备配置
   */
  private createMediumEndProfiles(): void {
    // 中端设备 - 简单楼层
    this.profiles.set(`${DevicePerformance.MEDIUM}_${SceneType.FLOOR_SIMPLE}`, {
      enabled: true,
      maxTriangles: 30000,
      mergeDifferentMaterials: false,
      verbose: false,
      enableLOD: true,
      lodDistances: [30, 80, 200],
      enableGeometryCompression: true,
      enableFrustumCulling: true,
      minMergeDistance: 5,
      enableInstancing: true,
      aggressiveOptimization: false,
      preserveHighDetail: false,
      batchSizeMultiplier: 0.8,
      compressionLevel: 3,
    });

    // 中端设备 - 复杂楼层
    this.profiles.set(`${DevicePerformance.MEDIUM}_${SceneType.FLOOR_COMPLEX}`, {
      enabled: true,
      maxTriangles: 25000,
      mergeDifferentMaterials: false,
      verbose: false,
      enableLOD: true,
      lodDistances: [25, 70, 180],
      enableGeometryCompression: true,
      enableFrustumCulling: true,
      minMergeDistance: 4,
      enableInstancing: true,
      aggressiveOptimization: false,
      preserveHighDetail: false,
      batchSizeMultiplier: 0.7,
      compressionLevel: 3,
    });

    // 中端设备 - 建筑外观
    this.profiles.set(`${DevicePerformance.MEDIUM}_${SceneType.BUILDING_EXTERIOR}`, {
      enabled: true,
      maxTriangles: 50000,
      mergeDifferentMaterials: true,
      verbose: false,
      enableLOD: true,
      lodDistances: [80, 250, 600],
      enableGeometryCompression: true,
      enableFrustumCulling: true,
      minMergeDistance: 15,
      enableInstancing: true,
      aggressiveOptimization: false,
      preserveHighDetail: false,
      batchSizeMultiplier: 1.0,
      compressionLevel: 2,
    });
  }

  /**
   * 创建高端设备配置
   */
  private createHighEndProfiles(): void {
    // 高端设备 - 简单楼层
    this.profiles.set(`${DevicePerformance.HIGH}_${SceneType.FLOOR_SIMPLE}`, {
      enabled: true,
      maxTriangles: 60000,
      mergeDifferentMaterials: false,
      verbose: false,
      enableLOD: true,
      lodDistances: [50, 150, 400],
      enableGeometryCompression: false,
      enableFrustumCulling: true,
      minMergeDistance: 8,
      enableInstancing: true,
      aggressiveOptimization: false,
      preserveHighDetail: true,
      batchSizeMultiplier: 1.2,
      compressionLevel: 1,
    });

    // 高端设备 - 复杂楼层
    this.profiles.set(`${DevicePerformance.HIGH}_${SceneType.FLOOR_COMPLEX}`, {
      enabled: true,
      maxTriangles: 50000,
      mergeDifferentMaterials: false,
      verbose: false,
      enableLOD: true,
      lodDistances: [40, 120, 300],
      enableGeometryCompression: false,
      enableFrustumCulling: true,
      minMergeDistance: 6,
      enableInstancing: true,
      aggressiveOptimization: false,
      preserveHighDetail: true,
      batchSizeMultiplier: 1.1,
      compressionLevel: 1,
    });

    // 高端设备 - 建筑外观
    this.profiles.set(`${DevicePerformance.HIGH}_${SceneType.BUILDING_EXTERIOR}`, {
      enabled: true,
      maxTriangles: 100000,
      mergeDifferentMaterials: false,
      verbose: false,
      enableLOD: true,
      lodDistances: [120, 400, 1000],
      enableGeometryCompression: false,
      enableFrustumCulling: true,
      minMergeDistance: 25,
      enableInstancing: true,
      aggressiveOptimization: false,
      preserveHighDetail: true,
      batchSizeMultiplier: 1.3,
      compressionLevel: 1,
    });
  }

  /**
   * 创建超高端设备配置
   */
  private createUltraEndProfiles(): void {
    // 超高端设备 - 复杂楼层
    this.profiles.set(`${DevicePerformance.ULTRA}_${SceneType.FLOOR_COMPLEX}`, {
      enabled: true,
      maxTriangles: 80000,
      mergeDifferentMaterials: false,
      verbose: true,
      enableLOD: false, // 超高端设备可以不使用LOD
      lodDistances: [],
      enableGeometryCompression: false,
      enableFrustumCulling: true,
      minMergeDistance: 8,
      enableInstancing: true,
      aggressiveOptimization: false,
      preserveHighDetail: true,
      batchSizeMultiplier: 1.5,
      compressionLevel: 1,
    });

    // 超高端设备 - 建筑外观
    this.profiles.set(`${DevicePerformance.ULTRA}_${SceneType.BUILDING_EXTERIOR}`, {
      enabled: true,
      maxTriangles: 150000,
      mergeDifferentMaterials: false,
      verbose: true,
      enableLOD: false,
      lodDistances: [],
      enableGeometryCompression: false,
      enableFrustumCulling: true,
      minMergeDistance: 30,
      enableInstancing: true,
      aggressiveOptimization: false,
      preserveHighDetail: true,
      batchSizeMultiplier: 1.8,
      compressionLevel: 1,
    });

    // 超高端设备 - 复杂混合场景
    this.profiles.set(`${DevicePerformance.ULTRA}_${SceneType.MIXED_COMPLEX}`, {
      enabled: true,
      maxTriangles: 120000,
      mergeDifferentMaterials: false,
      verbose: true,
      enableLOD: true,
      lodDistances: [200, 600, 1500],
      enableGeometryCompression: false,
      enableFrustumCulling: true,
      minMergeDistance: 20,
      enableInstancing: true,
      aggressiveOptimization: false,
      preserveHighDetail: true,
      batchSizeMultiplier: 1.6,
      compressionLevel: 1,
    });
  }

  /**
   * 获取指定配置
   */
  public getProfile(devicePerformance: DevicePerformance, sceneType: SceneType): ExtendedMergeOptions | null {
    const key = `${devicePerformance}_${sceneType}`;
    return this.profiles.get(key) || null;
  }

  /**
   * 获取推荐配置（自动检测设备性能）
   */
  public getRecommendedProfile(sceneType: SceneType): ExtendedMergeOptions {
    const devicePerformance = this.detectDevicePerformance();
    const profile = this.getProfile(devicePerformance, sceneType);

    if (profile) {
      return profile;
    }

    // 如果没有找到精确匹配，返回中端设备的通用配置
    return this.getFallbackProfile(devicePerformance);
  }

  /**
   * 检测设备性能等级
   */
  private detectDevicePerformance(): DevicePerformance {
    // 检测硬件信息
    const canvas = document.createElement('canvas');
    const gl =
      (canvas.getContext('webgl') as WebGLRenderingContext | null) || (canvas.getContext('experimental-webgl') as WebGLRenderingContext | null);

    if (!gl) {
      return DevicePerformance.LOW;
    }

    // 获取GPU信息
    const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
    const renderer = debugInfo ? gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) : '';

    // 检测内存
    const memoryGB = (navigator as any).deviceMemory || 4; // 默认4GB

    // 检测CPU核心数
    const cpuCores = navigator.hardwareConcurrency || 4;

    // 基于硬件信息评估性能等级
    let score = 0;

    // GPU评分
    if (renderer.toLowerCase().includes('rtx') || renderer.toLowerCase().includes('radeon rx')) {
      score += 40;
    } else if (renderer.toLowerCase().includes('gtx') || renderer.toLowerCase().includes('radeon')) {
      score += 25;
    } else if (renderer.toLowerCase().includes('intel')) {
      score += 10;
    }

    // 内存评分
    if (memoryGB >= 16) score += 30;
    else if (memoryGB >= 8) score += 20;
    else if (memoryGB >= 4) score += 10;

    // CPU评分
    if (cpuCores >= 12) score += 20;
    else if (cpuCores >= 8) score += 15;
    else if (cpuCores >= 4) score += 10;

    // 移动设备检测
    const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    if (isMobile) score *= 0.6;

    // 根据分数返回性能等级
    if (score >= 80) return DevicePerformance.ULTRA;
    if (score >= 60) return DevicePerformance.HIGH;
    if (score >= 35) return DevicePerformance.MEDIUM;
    return DevicePerformance.LOW;
  }

  /**
   * 获取后备配置
   */
  private getFallbackProfile(devicePerformance: DevicePerformance): ExtendedMergeOptions {
    const baseProfiles = {
      [DevicePerformance.LOW]: {
        enabled: true,
        maxTriangles: 15000,
        mergeDifferentMaterials: true,
        verbose: false,
        enableLOD: true,
        lodDistances: [20, 60, 150],
        enableGeometryCompression: true,
        enableFrustumCulling: true,
        minMergeDistance: 5,
        enableInstancing: true,
        aggressiveOptimization: true,
        preserveHighDetail: false,
        batchSizeMultiplier: 0.6,
        compressionLevel: 5,
      },
      [DevicePerformance.MEDIUM]: {
        enabled: true,
        maxTriangles: 40000,
        mergeDifferentMaterials: false,
        verbose: false,
        enableLOD: true,
        lodDistances: [40, 120, 300],
        enableGeometryCompression: true,
        enableFrustumCulling: true,
        minMergeDistance: 8,
        enableInstancing: true,
        aggressiveOptimization: false,
        preserveHighDetail: false,
        batchSizeMultiplier: 1.0,
        compressionLevel: 3,
      },
      [DevicePerformance.HIGH]: {
        enabled: true,
        maxTriangles: 70000,
        mergeDifferentMaterials: false,
        verbose: false,
        enableLOD: true,
        lodDistances: [60, 180, 450],
        enableGeometryCompression: false,
        enableFrustumCulling: true,
        minMergeDistance: 12,
        enableInstancing: true,
        aggressiveOptimization: false,
        preserveHighDetail: true,
        batchSizeMultiplier: 1.2,
        compressionLevel: 1,
      },
      [DevicePerformance.ULTRA]: {
        enabled: true,
        maxTriangles: 120000,
        mergeDifferentMaterials: false,
        verbose: false,
        enableLOD: false,
        lodDistances: [],
        enableGeometryCompression: false,
        enableFrustumCulling: true,
        minMergeDistance: 15,
        enableInstancing: true,
        aggressiveOptimization: false,
        preserveHighDetail: true,
        batchSizeMultiplier: 1.6,
        compressionLevel: 1,
      },
    };

    return baseProfiles[devicePerformance];
  }

  /**
   * 创建自定义配置
   */
  public createCustomProfile(name: string, baseProfile: ExtendedMergeOptions, overrides: Partial<ExtendedMergeOptions>): void {
    const customProfile = { ...baseProfile, ...overrides };
    this.profiles.set(name, customProfile);
  }

  /**
   * 获取所有可用的配置名称
   */
  public getAvailableProfiles(): string[] {
    return Array.from(this.profiles.keys());
  }

  /**
   * 性能测试和自动调优
   */
  public async performanceTuning(scene: THREE.Object3D, targetFPS: number = 60): Promise<ExtendedMergeOptions> {
    const meshMerger = MeshMerger.getInstance();
    const testProfiles = [this.detectDevicePerformance()];

    let bestProfile: ExtendedMergeOptions = this.getFallbackProfile(DevicePerformance.MEDIUM);
    let bestFPS = 0;

    for (const deviceLevel of testProfiles) {
      const profile = this.getFallbackProfile(deviceLevel);

      // 应用配置并测试性能
      const startTime = performance.now();

      try {
        const result = meshMerger.enhancedMergeMeshes(scene, profile);
        const endTime = performance.now();

        // 简单的性能估算（基于处理时间和减少的mesh数量）
        const processingTime = endTime - startTime;
        const efficiency = result.originalMeshCount > 0 ? (result.originalMeshCount - result.mergedMeshCount) / processingTime : 0;

        const estimatedFPS = Math.min(targetFPS, Math.max(30, 60 - processingTime / 10));

        if (estimatedFPS > bestFPS && estimatedFPS >= targetFPS * 0.8) {
          bestFPS = estimatedFPS;
          bestProfile = profile;
        }

        console.log(`[性能调优] ${deviceLevel} 配置测试完成: 估算FPS ${estimatedFPS.toFixed(1)}, 效率 ${efficiency.toFixed(2)}`);
      } catch (error) {
        console.warn(`[性能调优] ${deviceLevel} 配置测试失败:`, error);
      }
    }

    console.log(`[性能调优] 最佳配置选择完成, 预计FPS: ${bestFPS.toFixed(1)}`);
    return bestProfile;
  }

  /**
   * 导出配置为JSON
   */
  public exportProfile(profileName: string): string | null {
    const profile = this.profiles.get(profileName);
    return profile ? JSON.stringify(profile, null, 2) : null;
  }

  /**
   * 从JSON导入配置
   */
  public importProfile(name: string, jsonConfig: string): boolean {
    try {
      const config = JSON.parse(jsonConfig) as ExtendedMergeOptions;
      this.profiles.set(name, config);
      return true;
    } catch (error) {
      console.error('[配置导入] JSON解析失败:', error);
      return false;
    }
  }
}
