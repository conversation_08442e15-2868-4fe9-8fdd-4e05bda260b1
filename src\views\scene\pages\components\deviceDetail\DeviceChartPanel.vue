<template>
  <div
    class="device-chart-panel fixed bg-[#15274D]/95 backdrop-blur-sm rounded-lg shadow-2xl border border-[#246CF9]/30 z-50 flex flex-col overflow-hidden"
    :style="{
      left: position.x + 'px',
      top: position.y + 'px',
      width: '22vw',
      height: '16vw',
    }"
  >
    <!-- 关闭按钮 -->
    <button
      @click="$emit('close')"
      class="absolute top-[0.4vw] right-[0.4vw] w-[1.2vw] h-[1.2vw] bg-red-500/80 hover:bg-red-500 rounded-full flex items-center justify-center transition-colors z-10"
    >
      <CloseOutlined class="text-white text-[0.6vw]" />
    </button>

    <!-- 标题栏（可拖拽） -->
    <div class="mb-[0.6vw] cursor-move select-none" @mousedown="startDrag">
      <h3 class="text-white text-[0.8vw] font-medium truncate pr-[2vw]">{{ chartData?.name || '设备数据' }}</h3>
      <p class="text-white/70 text-[0.6vw] truncate">{{ chartData?.remark || '' }}</p>
    </div>

    <!-- 时间选择器 -->
    <div class="mb-[0.4vw] flex-shrink-0">
      <div class="flex flex-wrap gap-[0.2vw] mb-[0.3vw]">
        <button
          v-for="preset in timePresets"
          :key="preset.value"
          @click="selectTimeRange(preset.value)"
          :class="[
            'px-[0.4vw] py-[0.1vw] text-[0.5vw] rounded transition-colors whitespace-nowrap',
            selectedTimeRange === preset.value ? 'bg-[#246CF9] text-white' : 'bg-[#1E2A47]/60 text-white/80 hover:bg-[#246CF9]/60',
          ]"
        >
          {{ preset.label }}
        </button>
      </div>

      <!-- 自定义时间范围 -->
      <div v-if="selectedTimeRange === 'custom'" class="flex gap-[0.2vw] items-center">
        <input
          v-model="customStartTime"
          type="date"
          class="flex-1 px-[0.3vw] py-[0.1vw] bg-[#1E2A47]/60 border border-[#246CF9]/20 rounded text-white text-[0.5vw] focus:border-[#246CF9]/60 outline-none min-w-0"
        />
        <span class="text-white/70 text-[0.5vw] whitespace-nowrap">至</span>
        <input
          v-model="customEndTime"
          type="date"
          class="flex-1 px-[0.3vw] py-[0.1vw] bg-[#1E2A47]/60 border border-[#246CF9]/20 rounded text-white text-[0.5vw] focus:border-[#246CF9]/60 outline-none min-w-0"
        />
        <button
          @click="applyCustomTimeRange"
          class="px-[0.4vw] py-[0.1vw] bg-[#246CF9] text-white text-[0.5vw] rounded hover:bg-[#246CF9]/80 transition-colors whitespace-nowrap"
        >
          应用
        </button>
      </div>
    </div>

    <!-- 图表容器 -->
    <div class="chart-container flex-1 relative overflow-hidden">
      <!-- 图表DOM元素始终存在，避免条件渲染导致的问题 -->
      <div ref="chartRef" class="w-full h-full"></div>

      <!-- 加载状态覆盖层 -->
      <div v-if="loading" class="absolute inset-0 flex items-center justify-center bg-[#15274D]/80">
        <div class="text-white/70 text-[0.7vw]">加载中...</div>
      </div>

      <!-- 错误状态覆盖层 -->
      <div v-else-if="error" class="absolute inset-0 flex items-center justify-center bg-[#15274D]/80">
        <div class="text-red-400 text-[0.7vw]">{{ error }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
  import { CloseOutlined } from '@ant-design/icons-vue';
  import * as echarts from 'echarts';
  import { getDeviceHistoryData, type DeviceHistoryParams, type DeviceHistoryResponse, type HistoryDataPoint } from '/@/api/ba/device-data';
  import { message } from 'ant-design-vue';

  interface Props {
    deviceId: string;
    deviceType: number;
    deviceName?: string;
    deviceRemark?: string;
    position: { x: number; y: number };
  }

  const props = defineProps<Props>();
  defineEmits<{
    close: [];
  }>();

  const chartRef = ref<HTMLElement | null>(null);
  let chartInstance: echarts.ECharts | null = null;

  const loading = ref(false);
  const error = ref<string | null>(null);
  const chartData = ref<DeviceHistoryResponse | null>(null);
  const selectedTimeRange = ref<string>('24h');
  const customStartTime = ref('');
  const customEndTime = ref('');
  const customDays = ref<number>(1); // 存储自定义时间范围的天数

  // 拖拽相关状态
  const isDragging = ref(false);
  const dragOffset = ref({ x: 0, y: 0 });
  const position = ref(props.position);

  // 防抖定时器
  let loadDataTimer: NodeJS.Timeout | null = null;

  // 防止频繁调用 setOption 的锁
  let isSettingOption = false;
  let pendingOptionUpdate = false;

  // 配置常量
  const CONFIG = {
    MAX_INIT_RETRIES: 3,
    INIT_RETRY_DELAY: 200,
    UPDATE_RETRY_DELAY: 100,
    DEBOUNCE_DELAY: 300,
    MAX_DATA_POINTS: 50,
    CHART_COLORS: {
      PRIMARY: '#246CF9',
      AREA_START: 'rgba(36, 108, 249, 0.3)',
      AREA_END: 'rgba(36, 108, 249, 0.05)',
    },
  } as const;

  // 时间预设选项
  const timePresets = [
    { label: '24小时', value: '24h' },
    { label: '7天', value: '7d' },
    { label: '30天', value: '30d' },
    { label: '自定义', value: 'custom' },
  ] as const;

  // 开发环境日志函数
  const isDev = process.env.NODE_ENV === 'development';
  const devLog = (message: string, ...args: any[]) => {
    if (isDev) {
      console.log(`[DeviceChartPanel] ${message}`, ...args);
    }
  };

  const devError = (message: string, ...args: any[]) => {
    if (isDev) {
      console.error(`[DeviceChartPanel] ${message}`, ...args);
    }
  };

  // 重试计数器，防止无限重试
  let initRetryCount = 0;

  // 初始化图表
  const initChart = () => {
    devLog('初始化图表');
    devLog('chartRef存在:', !!chartRef.value);

    if (!chartRef.value) {
      if (initRetryCount < CONFIG.MAX_INIT_RETRIES) {
        initRetryCount++;
        devError(`chartRef不存在，第${initRetryCount}次重试初始化图表`);
        setTimeout(() => {
          initChart();
        }, CONFIG.INIT_RETRY_DELAY);
        return;
      } else {
        devError('chartRef初始化失败，已达到最大重试次数');
        return;
      }
    }

    try {
      chartInstance = echarts.init(chartRef.value);
      devLog('ECharts实例创建成功:', !!chartInstance);
    } catch (error) {
      devError('ECharts实例创建失败:', error);
    }
    // 设置基础配置
    const option: echarts.EChartsOption = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#246CF9',
        textStyle: {
          color: '#fff',
          fontSize: 12,
        },
        formatter: (params: any) => {
          if (Array.isArray(params) && params.length > 0) {
            const point = params[0];
            return `${point.name}<br/>${point.seriesName}: ${point.value}${chartData.value?.unit || ''}`;
          }
          return '';
        },
      },
      grid: {
        top: '10%',
        left: '8%',
        right: '5%',
        bottom: '35%',
      },
      xAxis: {
        type: 'category',
        data: [],
        axisLabel: {
          color: '#fff',
          fontSize: 10,
          rotate: 45,
          interval: 'auto', // 自动间隔显示标签
          margin: 12, // 增加标签与轴线的距离
          formatter: (value: string) => {
            // 如果标签太长，进一步缩短
            return value.length > 10 ? value.substring(0, 10) + '...' : value;
          },
        },
        axisLine: {
          lineStyle: {
            color: '#246CF9',
          },
        },
        axisTick: {
          alignWithLabel: true,
        },
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          color: '#fff',
          fontSize: 10,
        },
        axisLine: {
          lineStyle: {
            color: '#246CF9',
          },
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(36, 108, 249, 0.2)',
          },
        },
      },
      series: [
        {
          name: chartData.value?.remark || '数值',
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 4,
          lineStyle: {
            color: CONFIG.CHART_COLORS.PRIMARY,
            width: 2,
          },
          itemStyle: {
            color: CONFIG.CHART_COLORS.PRIMARY,
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: CONFIG.CHART_COLORS.AREA_START },
                { offset: 1, color: CONFIG.CHART_COLORS.AREA_END },
              ],
            },
          },
          data: [],
        },
      ],
    };

    if (chartInstance) {
      // 如果已经在设置选项，直接返回
      if (isSettingOption) {
        devLog('已有setOption在执行，跳过初始化配置');
        return;
      }

      isSettingOption = true;

      // 使用nextTick确保在下一个DOM更新周期执行，避免在ECharts主进程中调用setOption
      nextTick(() => {
        if (chartInstance) {
          chartInstance.setOption(option);
          devLog('图表初始化配置设置成功');

          // 延迟释放锁，避免过快连续调用
          setTimeout(() => {
            isSettingOption = false;
          }, 100);
        } else {
          isSettingOption = false;
        }
      });
    }
  };

  // 更新图表数据
  const updateChart = () => {
    devLog('updateChart 被调用');

    if (!chartInstance) {
      devError('chartInstance不存在，无法更新图表');
      return;
    }

    if (!chartData.value) {
      devError('chartData不存在，无法更新图表');
      // 清除图表数据
      clearChart();
      return;
    }

    // 如果数据为空，清除图表并显示空状态
    if (!chartData.value.data || chartData.value.data.length === 0) {
      devLog('数据为空，清除图表显示');
      clearChart();
      return;
    }

    const xAxisData = chartData.value.data.map((point: HistoryDataPoint) => point.time);
    const seriesData = chartData.value.data.map((point: HistoryDataPoint) => point.value);

    devLog('数据点数量:', chartData.value.data.length);

    // 使用完整的option配置，确保数据被完全替换
    const option = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#246CF9',
        textStyle: {
          color: '#fff',
          fontSize: 12,
        },
        formatter: (params: any) => {
          if (Array.isArray(params) && params.length > 0) {
            const point = params[0];
            return `${point.name}<br/>${point.seriesName}: ${point.value}${chartData.value?.unit || ''}`;
          }
          return '';
        },
      },
      grid: {
        top: '10%',
        left: '8%',
        right: '5%',
        bottom: '35%',
      },
      xAxis: {
        type: 'category',
        data: xAxisData,
        axisLabel: {
          color: '#fff',
          fontSize: 10,
          rotate: 45,
          interval: 'auto',
          margin: 12,
          formatter: (value: string) => {
            return value.length > 10 ? value.substring(0, 10) + '...' : value;
          },
        },
        axisLine: {
          lineStyle: {
            color: '#246CF9',
          },
        },
        axisTick: {
          alignWithLabel: true,
        },
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          color: '#fff',
          fontSize: 10,
        },
        axisLine: {
          lineStyle: {
            color: '#246CF9',
          },
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(36, 108, 249, 0.2)',
          },
        },
      },
      series: [
        {
          name: chartData.value.remark || '数值',
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 4,
          lineStyle: {
            color: CONFIG.CHART_COLORS.PRIMARY,
            width: 2,
          },
          itemStyle: {
            color: CONFIG.CHART_COLORS.PRIMARY,
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: CONFIG.CHART_COLORS.AREA_START },
                { offset: 1, color: CONFIG.CHART_COLORS.AREA_END },
              ],
            },
          },
          data: seriesData,
        },
      ],
    };

    try {
      // 如果已经在设置选项，标记为待更新并返回
      if (isSettingOption) {
        pendingOptionUpdate = true;
        devLog('已有setOption在执行，标记为待更新');
        return;
      }

      isSettingOption = true;

      // 使用nextTick确保在下一个DOM更新周期执行，避免在ECharts主进程中调用setOption
      nextTick(() => {
        if (chartInstance) {
          // 使用notMerge: true 确保完全替换数据，不与旧数据合并
          chartInstance.setOption(option, { notMerge: true });
          devLog('图表更新成功');

          // 延迟释放锁，避免过快连续调用
          setTimeout(() => {
            isSettingOption = false;

            // 如果有待处理的更新，重新调用updateChart
            if (pendingOptionUpdate) {
              pendingOptionUpdate = false;
              devLog('处理待更新的setOption请求');
              updateChart();
            }
          }, 100);
        } else {
          isSettingOption = false;
        }
      });
    } catch (err) {
      devError('图表更新失败:', err);
      isSettingOption = false;
    }
  };

  // 清除图表数据
  const clearChart = () => {
    if (!chartInstance) {
      return;
    }

    devLog('清除图表数据');

    const emptyOption = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#246CF9',
        textStyle: {
          color: '#fff',
          fontSize: 12,
        },
        formatter: (params: any) => {
          if (Array.isArray(params) && params.length > 0) {
            const point = params[0];
            return `${point.name}<br/>${point.seriesName}: ${point.value}${chartData.value?.unit || ''}`;
          }
          return '';
        },
      },
      grid: {
        top: '10%',
        left: '8%',
        right: '5%',
        bottom: '35%',
      },
      xAxis: {
        type: 'category',
        data: [],
        axisLabel: {
          color: '#fff',
          fontSize: 10,
          rotate: 45,
          interval: 'auto',
          margin: 12,
          formatter: (value: string) => {
            return value.length > 10 ? value.substring(0, 10) + '...' : value;
          },
        },
        axisLine: {
          lineStyle: {
            color: '#246CF9',
          },
        },
        axisTick: {
          alignWithLabel: true,
        },
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          color: '#fff',
          fontSize: 10,
        },
        axisLine: {
          lineStyle: {
            color: '#246CF9',
          },
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(36, 108, 249, 0.2)',
          },
        },
      },
      series: [
        {
          name: chartData.value?.remark || '数值',
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 4,
          lineStyle: {
            color: CONFIG.CHART_COLORS.PRIMARY,
            width: 2,
          },
          itemStyle: {
            color: CONFIG.CHART_COLORS.PRIMARY,
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: CONFIG.CHART_COLORS.AREA_START },
                { offset: 1, color: CONFIG.CHART_COLORS.AREA_END },
              ],
            },
          },
          data: [],
        },
      ],
    };

    try {
      // 如果已经在设置选项，直接返回，避免重复调用
      if (isSettingOption) {
        devLog('已有setOption在执行，跳过清除操作');
        return;
      }

      isSettingOption = true;

      // 使用nextTick确保在下一个DOM更新周期执行，避免在ECharts主进程中调用setOption
      nextTick(() => {
        if (chartInstance) {
          // 使用notMerge: true 确保完全清除旧数据
          chartInstance.setOption(emptyOption, { notMerge: true });
          devLog('图表数据清除成功');

          // 延迟释放锁，避免过快连续调用
          setTimeout(() => {
            isSettingOption = false;
          }, 100);
        } else {
          isSettingOption = false;
        }
      });
    } catch (err) {
      devError('图表数据清除失败:', err);
      isSettingOption = false;
    }
  };

  // 将时间范围转换为天数
  const getTimeRangeDays = (timeRange: string): number => {
    switch (timeRange) {
      case '1h':
      case '6h':
      case '12h':
      case '24h':
        return 1;
      case '7d':
        return 7;
      case '30d':
        return 30;
      case 'custom':
        return customDays.value; // 使用自定义计算的天数
      default:
        return 1;
    }
  };

  // 格式化时间显示
  const formatTimeForDisplay = (timeStr: string): string => {
    if (!timeStr) return '';

    try {
      const date = new Date(timeStr);
      const timeRange = selectedTimeRange.value;

      // 根据时间范围选择不同的显示格式
      switch (timeRange) {
        case '1h':
        case '6h':
        case '12h':
          // 短时间范围：只显示时:分
          return date.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false,
          });
        case '24h':
          // 24小时：显示月-日 时:分
          return (
            date.toLocaleDateString('zh-CN', {
              month: '2-digit',
              day: '2-digit',
            }) +
            ' ' +
            date.toLocaleTimeString('zh-CN', {
              hour: '2-digit',
              minute: '2-digit',
              hour12: false,
            })
          );
        case '7d':
          // 7天：显示月-日
          return date.toLocaleDateString('zh-CN', {
            month: '2-digit',
            day: '2-digit',
          });
        case '30d':
          // 30天：显示月-日
          return date.toLocaleDateString('zh-CN', {
            month: '2-digit',
            day: '2-digit',
          });
        default:
          // 默认：显示月-日 时:分
          return (
            date.toLocaleDateString('zh-CN', {
              month: '2-digit',
              day: '2-digit',
            }) +
            ' ' +
            date.toLocaleTimeString('zh-CN', {
              hour: '2-digit',
              minute: '2-digit',
              hour12: false,
            })
          );
      }
    } catch (error) {
      console.error('[DeviceChartPanel] 时间格式化失败:', error, timeStr);
      return timeStr;
    }
  };

  // 数据采样函数 - 当数据点过多时进行采样
  const sampleData = (data: HistoryDataPoint[], maxPoints: number = 50): HistoryDataPoint[] => {
    if (data.length <= maxPoints) {
      return data;
    }

    const step = Math.ceil(data.length / maxPoints);
    const sampledData: HistoryDataPoint[] = [];

    for (let i = 0; i < data.length; i += step) {
      sampledData.push(data[i]);
    }

    // 确保包含最后一个数据点
    const lastOriginal = data[data.length - 1];
    const lastSampled = sampledData[sampledData.length - 1];
    if (lastSampled !== lastOriginal) {
      sampledData.push(lastOriginal);
    }

    devLog(`数据采样：${data.length} -> ${sampledData.length}`);
    return sampledData;
  };

  // 加载图表数据
  const loadChartData = async () => {
    if (!props.deviceId) {
      devLog('deviceId为空，无法加载数据');
      return;
    }

    devLog('开始加载图表数据', {
      deviceId: props.deviceId,
      deviceType: props.deviceType,
      deviceName: props.deviceName,
      deviceRemark: props.deviceRemark,
    });

    loading.value = true;
    error.value = null;

    // 在加载新数据前先清除图表，避免显示旧数据
    // 使用标志位避免重复调用
    const wasClearedBeforeLoad = true;
    clearChart();

    try {
      const params: DeviceHistoryParams = {
        id: props.deviceId,
        type: props.deviceType,
        day: getTimeRangeDays(selectedTimeRange.value),
      };

      devLog('请求参数:', params);
      devLog('选择的时间范围:', selectedTimeRange.value);

      const response = await getDeviceHistoryData(params);
      devLog('API响应数据量:', response?.length || 0);

      // 处理API返回的数据格式
      // API返回的是数组，需要转换为我们期望的格式
      if (Array.isArray(response) && response.length > 0) {
        // 先转换数据格式
        const rawData = response.map((item) => ({
          time: formatTimeForDisplay(item.dataTime || item.time || ''),
          value: parseFloat(item.valueData || item.value || 0),
        }));

        // 对数据进行采样以优化显示效果
        const sampledData = sampleData(rawData, CONFIG.MAX_DATA_POINTS);

        chartData.value = {
          id: props.deviceId,
          name: props.deviceName || '设备数据',
          remark: props.deviceRemark || '数值',
          unit: response[0].unit || '', // 从第一个数据点获取单位
          data: sampledData,
        };
      } else {
        // 如果没有数据，创建空的数据结构
        chartData.value = {
          id: props.deviceId,
          name: props.deviceName || '设备数据',
          remark: props.deviceRemark || '数值',
          unit: '',
          data: [],
        };
      }

      devLog('处理后的数据点数量:', chartData.value?.data?.length || 0);

      // 更新图表
      devLog('准备更新图表');

      // 如果chartInstance还没有准备好，等待一下再尝试
      if (!chartInstance) {
        devLog('chartInstance未准备好，等待后重试');
        setTimeout(() => {
          devLog('重试更新图表');
          updateChart();
        }, CONFIG.UPDATE_RETRY_DELAY);
      } else {
        updateChart();
      }
    } catch (err) {
      devError('加载图表数据失败:', err);
      error.value = '加载图表数据失败';
      message.error('加载图表数据失败');
    } finally {
      loading.value = false;
      devLog('数据加载完成');
    }
  };

  // 防抖加载数据
  const debouncedLoadChartData = () => {
    if (loadDataTimer) {
      clearTimeout(loadDataTimer);
    }
    loadDataTimer = setTimeout(() => {
      loadChartData();
    }, CONFIG.DEBOUNCE_DELAY);
  };

  // 选择时间范围
  const selectTimeRange = (range: string) => {
    selectedTimeRange.value = range;
    if (range !== 'custom') {
      // 立即清除图表，避免在加载新数据期间显示旧数据
      clearChart();
      // 确保在清除图表后，延迟一点时间再加载数据
      setTimeout(() => {
        debouncedLoadChartData();
      }, 50);
    }
  };

  // 应用自定义时间范围
  const applyCustomTimeRange = () => {
    if (!customStartTime.value || !customEndTime.value) {
      message.warning('请选择开始和结束时间');
      return;
    }

    if (new Date(customStartTime.value) >= new Date(customEndTime.value)) {
      message.warning('开始时间必须早于结束时间');
      return;
    }

    // 计算时间差，转换为天数
    const startDate = new Date(customStartTime.value);
    const endDate = new Date(customEndTime.value);
    const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    // 更新自定义天数
    customDays.value = diffDays;

    devLog('自定义时间范围:', { startDate, endDate, diffDays });

    // 立即清除图表，避免在加载新数据期间显示旧数据
    clearChart();
    // 确保在清除图表后，延迟一点时间再加载数据
    setTimeout(() => {
      loadChartData();
    }, 50);
  };

  // 初始化自定义时间
  const initCustomTime = () => {
    const now = new Date();
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    // 由于输入框类型改为date，只需要日期部分
    customEndTime.value = now.toISOString().slice(0, 10);
    customStartTime.value = yesterday.toISOString().slice(0, 10);
  };

  // 拖拽功能
  const startDrag = (event: MouseEvent) => {
    isDragging.value = true;
    dragOffset.value = {
      x: event.clientX - position.value.x,
      y: event.clientY - position.value.y,
    };

    const handleMouseMove = (e: MouseEvent) => {
      if (isDragging.value) {
        position.value = {
          x: e.clientX - dragOffset.value.x,
          y: e.clientY - dragOffset.value.y,
        };
      }
    };

    const handleMouseUp = () => {
      isDragging.value = false;
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  // 监听设备变化
  watch(
    () => [props.deviceId, props.deviceType],
    () => {
      if (props.deviceId) {
        loadChartData();
      }
    },
    { immediate: true }
  );

  // 监听位置变化
  watch(
    () => props.position,
    (newPosition) => {
      position.value = { ...newPosition };
    },
    { immediate: true }
  );

  // 监听窗口大小变化的处理函数
  const handleResize = () => {
    chartInstance?.resize();
  };

  onMounted(async () => {
    devLog('组件挂载', {
      deviceId: props.deviceId,
      deviceType: props.deviceType,
      deviceName: props.deviceName,
      deviceRemark: props.deviceRemark,
    });

    // 等待DOM渲染完成
    await nextTick();
    devLog('DOM渲染完成，准备初始化图表');

    initChart();
    initCustomTime();

    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize);
  });

  // 清理函数放在外面，避免异步问题
  onUnmounted(() => {
    devLog('组件卸载');

    // 清理定时器
    if (loadDataTimer) {
      clearTimeout(loadDataTimer);
      loadDataTimer = null;
    }

    // 清理事件监听器和图表实例
    window.removeEventListener('resize', handleResize);

    if (chartInstance) {
      try {
        chartInstance.dispose();
      } catch (err) {
        devError('图表实例销毁失败:', err);
      }
      chartInstance = null;
    }
  });
</script>

<style scoped>
  .device-chart-panel {
    padding: 0.8vw;
    animation: fadeInScale 0.2s ease-out;
  }

  @keyframes fadeInScale {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  .chart-container {
    border: 1px solid rgba(36, 108, 249, 0.2);
    border-radius: 4px;
  }

  /* 日期选择器样式优化 */
  input[type='date'] {
    color-scheme: dark;
    position: relative;
  }

  /* 日期选择器图标样式 */
  input[type='date']::-webkit-calendar-picker-indicator {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%23ffffff'%3e%3cpath fill-rule='evenodd' d='M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z' clip-rule='evenodd'/%3e%3c/svg%3e");
    background-size: 1em 1em;
    background-repeat: no-repeat;
    background-position: center;
    cursor: pointer;
    opacity: 0.8;
    transition: opacity 0.2s;
  }

  input[type='date']::-webkit-calendar-picker-indicator:hover {
    opacity: 1;
  }

  /* Firefox 日期选择器样式 */
  input[type='date']::-moz-calendar-picker-indicator {
    filter: invert(1);
    opacity: 0.8;
  }

  /* 自定义滚动条 */
  .device-chart-panel::-webkit-scrollbar {
    width: 0.3vw;
  }

  .device-chart-panel::-webkit-scrollbar-track {
    background: rgba(21, 39, 77, 0.3);
    border-radius: 0.15vw;
  }

  .device-chart-panel::-webkit-scrollbar-thumb {
    background-color: rgba(36, 108, 249, 0.5);
    border-radius: 0.15vw;
  }
</style>
