<template>
  <div class="suggest-area">
    <div v-if="loading" class="loading">
      <a-spin size="small" />
    </div>
    <template v-else>
      <a-button
        v-for="(item, index) in suggestions"
        :key="index"
        type="dashed"
        size="small"
        @click="handleClick(item)"
        class="suggest-btn"
        v-show="showSuggestions"
      >
        {{ item }}
      </a-button>
    </template>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, inject, watch, computed } from 'vue';
  import { XAI_CONFIG, getModelApiKey } from '/@/enums/httpEnum';

  const emit = defineEmits<{
    (e: 'outQuestion', question: string): void;
  }>();

  const isGenerating = ref<boolean>(false);
  const suggestions = ref<string[]>([]);
  const showSuggestions = ref<boolean>(true);

  const currentModel = inject('currentModel', XAI_CONFIG);
  const modelKey = computed(() => currentModel.MODEL || 'GROK');

  interface ChatData {
    inversion: boolean;
    text: string;
  }

  const props = defineProps<{
    chatData: ChatData[];
    loading: boolean;
  }>();

  async function generateSuggestions() {
    if (props.loading) {
      showSuggestions.value = false;
      return;
    }

    // 默认初始问题
    if (props.chatData.length === 0) {
      suggestions.value = ['JEECG有哪些特点？', '如何快速上手？', '常见问题有哪些？'];
      showSuggestions.value = true;
      return;
    }

    isGenerating.value = true;
    showSuggestions.value = false;

    try {
      // 获取最近的AI回复
      const lastAiMessage = props.chatData.findLast((chat) => !chat.inversion)?.text || '';
      if (!lastAiMessage) {
        suggestions.value = generateFallbackSuggestions();
        showSuggestions.value = true;
        isGenerating.value = false;
        return;
      }
      const apiKey = getModelApiKey(modelKey.value) || currentModel.API_KEY;
      if (!apiKey) {
        suggestions.value = generateFallbackSuggestions(lastAiMessage);
        showSuggestions.value = true;
        isGenerating.value = false;
        return;
      }
      const response = await fetch('/ai-api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${apiKey}`,
        },
        body: JSON.stringify({
          model: currentModel.MODEL,
          messages: [
            {
              role: 'system',
              content: `你是一个提问建议生成器。基于AI的上一个回答内容，生成3个高质量的后续提问建议：
              规则：
              1. 问题必须与上文内容紧密相关，体现递进关系
              2. 问题要简短，不超过15个字
              3. 问题要以"如何"、"能否"、"请问"等词开头
              4. 按照由浅入深的顺序排列
              5. questions格式用|分隔
              6. 直接返回问题文本，不要其他解释
              7. 避免重复已经提到的内容
              8. 问题要有实用价值，引导用户深入了解`,
            },
            {
              role: 'assistant',
              content: lastAiMessage,
            },
          ],
          stream: false,
          temperature: 0.8,
          ...currentModel.options,
        }),
      });

      if (!response.ok) {
        throw new Error('生成建议问题失败');
      }

      const result = await response.json();
      const content = result.choices[0].message.content;

      // 处理生成的问题
      const questions = content
        .split('|')
        .map((q: string) => q.trim())
        .filter((q: string) => {
          // 确保问题符合要求
          const isValidLength = q.length <= 15;
          const hasValidPrefix = /^(如何|能否|请问|还有|那么|为什么)/.test(q);
          return isValidLength && hasValidPrefix;
        });

      suggestions.value = questions.length > 0 ? questions : generateFallbackSuggestions(lastAiMessage);
      showSuggestions.value = true;
    } catch (error) {
      console.error('获取建议问题失败:', error);
      const lastMessage = props.chatData.findLast((chat) => !chat.inversion)?.text || '';
      suggestions.value = generateFallbackSuggestions(lastMessage);
      showSuggestions.value = true;
    } finally {
      isGenerating.value = false;
    }
  }

  function generateFallbackSuggestions(lastMessage: string = ''): string[] {
    if (!lastMessage) {
      return ['能详细解释一下吗？', '有具体示例吗？', '实际应用场景？'];
    }

    // 根据最后回复内容智能生成基础问题
    const keywords = extractKeywords(lastMessage);
    if (keywords.length > 0) {
      return [`如何使用${keywords[0]}？`, `${keywords[0]}的优势是？`, '有没有相关示例？'];
    }

    return ['能详细说明吗？', '有示例代码吗？', '应用场景是？'];
  }

  function extractKeywords(text: string): string[] {
    // 简单的关键词提取逻辑
    const words = text.match(/[a-zA-Z\u4e00-\u9fa5]+/g) || [];
    return words.filter((word: string) => word.length >= 2).slice(0, 2);
  }

  watch(
    [() => props.chatData, () => props.loading],
    ([newChatData, newLoading]) => {
      if (!newLoading && newChatData.length > 0) {
        setTimeout(() => {
          generateSuggestions();
        }, 100);
      }
    },
    { deep: true }
  );

  const handleClick = (question: string) => {
    emit('outQuestion', question);
    generateSuggestions();
  };

  onMounted(() => {
    generateSuggestions();
  });
</script>

<style lang="less" scoped>
  .suggest-area {
    min-height: 32px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;

    .loading {
      width: 100%;
      display: flex;
      justify-content: center;
    }

    .suggest-btn {
      font-size: 12px;
      padding: 0 12px;
      height: 28px;
      border-radius: 14px;
      white-space: nowrap;
      transition: all 0.3s;

      &:hover {
        transform: scale(1.05);
        background: #e6f4ff;
      }
    }
  }
</style>
