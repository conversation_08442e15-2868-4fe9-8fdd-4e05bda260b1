/**
 * 鼠标悬停设备面板调试工具
 * 用于检测和诊断鼠标移入设备时面板不出现的问题
 */

class MouseHoverDebugger {
  constructor() {
    this.isEnabled = false;
    this.debugInfo = {
      eventListeners: [],
      raycastResults: [],
      deviceInfoManager: null,
      objectSelection: null,
      lastMouseEvent: null,
      cacheStatus: {},
    };

    this.init();
  }

  init() {
    // 添加调试控制台命令
    window.mouseHoverDebugger = this;

    // 监听场景对象变化事件
    window.addEventListener('scene-objects-changed', () => {
      this.logEvent('scene-objects-changed event triggered');
      this.checkEventListeners();
    });

    console.log('[MouseHoverDebugger] 调试器已初始化');
    console.log('使用 window.mouseHoverDebugger.enable() 启用调试');
  }

  enable() {
    this.isEnabled = true;
    this.startMonitoring();
    console.log('[MouseHoverDebugger] 调试模式已启用');
  }

  disable() {
    this.isEnabled = false;
    this.stopMonitoring();
    console.log('[MouseHoverDebugger] 调试模式已禁用');
  }

  startMonitoring() {
    // 监听鼠标移动事件
    document.addEventListener('mousemove', this.onMouseMove.bind(this));

    // 定期检查状态
    this.statusCheckInterval = setInterval(() => {
      this.checkStatus();
    }, 2000);
  }

  stopMonitoring() {
    document.removeEventListener('mousemove', this.onMouseMove.bind(this));

    if (this.statusCheckInterval) {
      clearInterval(this.statusCheckInterval);
    }
  }

  onMouseMove(event) {
    if (!this.isEnabled) return;

    this.debugInfo.lastMouseEvent = {
      x: event.clientX,
      y: event.clientY,
      timestamp: Date.now(),
    };
  }

  checkStatus() {
    if (!this.isEnabled) return;

    try {
      // 检查 ObjectSelection 实例
      const ObjectSelection = window.ObjectSelection || (window.THREE && window.THREE.ObjectSelection);

      if (ObjectSelection && ObjectSelection.getInstance) {
        this.debugInfo.objectSelection = ObjectSelection.getInstance();
      }

      // 检查 DeviceInfoManager 实例
      const DeviceInfoManager = window.DeviceInfoManager || (window.THREE && window.THREE.DeviceInfoManager);

      if (DeviceInfoManager && DeviceInfoManager.getInstance) {
        this.debugInfo.deviceInfoManager = DeviceInfoManager.getInstance();
      }

      // 检查事件监听器
      this.checkEventListeners();

      // 检查缓存状态
      this.checkCacheStatus();
    } catch (error) {
      console.error('[MouseHoverDebugger] 状态检查失败:', error);
    }
  }

  checkEventListeners() {
    const rendererDom = document.querySelector('canvas') || document.querySelector('#three-container') || document.querySelector('.three-container');

    if (!rendererDom) {
      this.logEvent('未找到渲染器DOM元素');
      return;
    }

    // 检查是否有鼠标事件监听器
    const events = ['mousemove', 'mouseleave', 'dblclick'];
    const listenerInfo = {};

    events.forEach((eventType) => {
      // 这里只能检查我们能访问的信息
      listenerInfo[eventType] = {
        hasListener: true, // 假设有监听器，实际检查需要更复杂的方法
        element: rendererDom.tagName,
      };
    });

    this.debugInfo.eventListeners = listenerInfo;
    this.logEvent('事件监听器检查完成', listenerInfo);
  }

  checkCacheStatus() {
    if (this.debugInfo.objectSelection) {
      const objectHighlighter = this.debugInfo.objectSelection.objectHighlighter;
      if (objectHighlighter) {
        this.debugInfo.cacheStatus = {
          hasObjectHighlighter: true,
          interactableObjectsCount: objectHighlighter.interactableObjects?.length || 0,
        };
      }
    }
  }

  // 手动触发设备信息面板测试
  testDeviceInfoPanel(x = 100, y = 100) {
    if (!this.debugInfo.deviceInfoManager) {
      console.error('[MouseHoverDebugger] DeviceInfoManager 未找到');
      return;
    }

    // 创建模拟设备对象
    const mockDevice = {
      name: 'test-device',
      uuid: 'test-uuid-' + Date.now(),
      userData: {
        id: 'test-device-id',
        type: 'server',
      },
    };

    try {
      this.debugInfo.deviceInfoManager.showDeviceInfo(mockDevice, { x, y });
      this.logEvent('手动触发设备信息面板测试', { x, y });
    } catch (error) {
      console.error('[MouseHoverDebugger] 设备信息面板测试失败:', error);
    }
  }

  // 强制重新初始化事件监听器
  forceReinitializeEventListeners() {
    try {
      if (this.debugInfo.objectSelection) {
        this.debugInfo.objectSelection.reinitializeEventListeners?.();
        this.logEvent('强制重新初始化事件监听器');
      }

      // 触发场景对象变化事件
      window.dispatchEvent(new CustomEvent('scene-objects-changed'));
      this.logEvent('触发 scene-objects-changed 事件');
    } catch (error) {
      console.error('[MouseHoverDebugger] 重新初始化失败:', error);
    }
  }

  // 清理所有缓存
  forceClearCache() {
    try {
      if (this.debugInfo.objectSelection?.objectHighlighter) {
        this.debugInfo.objectSelection.objectHighlighter.forceCleanCache?.();
        this.logEvent('强制清理缓存');
      }

      if (this.debugInfo.deviceInfoManager) {
        this.debugInfo.deviceInfoManager.forceReset?.();
        this.logEvent('强制重置 DeviceInfoManager');
      }
    } catch (error) {
      console.error('[MouseHoverDebugger] 清理缓存失败:', error);
    }
  }

  // 获取调试报告
  getDebugReport() {
    const report = {
      timestamp: new Date().toISOString(),
      isEnabled: this.isEnabled,
      debugInfo: this.debugInfo,
      recommendations: this.getRecommendations(),
    };

    console.log('[MouseHoverDebugger] 调试报告:', report);
    return report;
  }

  getRecommendations() {
    const recommendations = [];

    if (!this.debugInfo.objectSelection) {
      recommendations.push('ObjectSelection 实例未找到，可能需要重新初始化');
    }

    if (!this.debugInfo.deviceInfoManager) {
      recommendations.push('DeviceInfoManager 实例未找到，可能需要重新初始化');
    }

    if (this.debugInfo.cacheStatus.interactableObjectsCount === 0) {
      recommendations.push('可交互对象数量为0，可能需要更新对象列表');
    }

    return recommendations;
  }

  logEvent(message, data = null) {
    const timestamp = new Date().toISOString();
    const logEntry = { timestamp, message, data };

    console.log(`[MouseHoverDebugger] ${timestamp}: ${message}`, data || '');

    // 保存到调试历史
    if (!this.debugInfo.eventHistory) {
      this.debugInfo.eventHistory = [];
    }
    this.debugInfo.eventHistory.push(logEntry);

    // 限制历史记录数量
    if (this.debugInfo.eventHistory.length > 50) {
      this.debugInfo.eventHistory.shift();
    }
  }
}

// 自动初始化调试器
if (typeof window !== 'undefined') {
  new MouseHoverDebugger();
}
