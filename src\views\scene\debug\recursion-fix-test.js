/**
 * 递归调用修复测试脚本
 * 用于验证 scene-objects-changed 事件的递归调用问题是否已修复
 */

class RecursionFixTester {
  constructor() {
    this.eventCallCount = 0;
    this.maxCallCount = 10; // 最大允许调用次数
    this.testStartTime = 0;
    this.isTestRunning = false;

    this.init();
  }

  init() {
    window.recursionFixTester = this;
    console.log('[RecursionFixTester] 递归修复测试器已初始化');
    console.log('使用 window.recursionFixTester.runTest() 开始测试');
  }

  runTest() {
    if (this.isTestRunning) {
      console.log('[RecursionFixTester] 测试已在运行中');
      return;
    }

    console.log('[RecursionFixTester] 开始递归调用测试...');
    this.isTestRunning = true;
    this.eventCallCount = 0;
    this.testStartTime = Date.now();

    // 监听 scene-objects-changed 事件
    this.eventListener = () => {
      this.eventCallCount++;
      console.log(`[RecursionFixTester] scene-objects-changed 事件被调用 ${this.eventCallCount} 次`);

      // 如果调用次数超过阈值，说明可能存在递归问题
      if (this.eventCallCount > this.maxCallCount) {
        console.error(`[RecursionFixTester] 检测到可能的递归调用！事件被调用了 ${this.eventCallCount} 次`);
        this.stopTest();
        return;
      }
    };

    window.addEventListener('scene-objects-changed', this.eventListener);

    // 手动触发事件来测试
    console.log('[RecursionFixTester] 触发 scene-objects-changed 事件...');
    window.dispatchEvent(new CustomEvent('scene-objects-changed'));

    // 设置超时，如果在合理时间内没有问题，认为测试通过
    setTimeout(() => {
      if (this.isTestRunning) {
        this.completeTest();
      }
    }, 2000);
  }

  stopTest() {
    if (!this.isTestRunning) return;

    this.isTestRunning = false;
    window.removeEventListener('scene-objects-changed', this.eventListener);

    const duration = Date.now() - this.testStartTime;
    console.log(`[RecursionFixTester] 测试停止，持续时间: ${duration}ms，总调用次数: ${this.eventCallCount}`);
  }

  completeTest() {
    if (!this.isTestRunning) return;

    this.isTestRunning = false;
    window.removeEventListener('scene-objects-changed', this.eventListener);

    const duration = Date.now() - this.testStartTime;

    if (this.eventCallCount <= this.maxCallCount) {
      console.log(`✅ [RecursionFixTester] 测试通过！`);
      console.log(`   - 持续时间: ${duration}ms`);
      console.log(`   - 事件调用次数: ${this.eventCallCount}`);
      console.log(`   - 没有检测到递归调用问题`);
    } else {
      console.error(`❌ [RecursionFixTester] 测试失败！`);
      console.error(`   - 事件调用次数过多: ${this.eventCallCount}`);
      console.error(`   - 可能存在递归调用问题`);
    }
  }

  // 测试楼层切换场景
  testFloorSwitch() {
    console.log('[RecursionFixTester] 开始楼层切换测试...');

    // 重置计数器
    this.eventCallCount = 0;
    this.testStartTime = Date.now();
    this.isTestRunning = true;

    // 监听事件
    this.eventListener = () => {
      this.eventCallCount++;
      console.log(`[RecursionFixTester] 楼层切换测试 - 事件调用 ${this.eventCallCount} 次`);
    };

    window.addEventListener('scene-objects-changed', this.eventListener);

    // 模拟楼层切换触发的事件序列
    setTimeout(() => {
      console.log('[RecursionFixTester] 模拟楼层切换完成事件...');
      window.dispatchEvent(new CustomEvent('scene-objects-changed'));
    }, 100);

    setTimeout(() => {
      console.log('[RecursionFixTester] 模拟延迟重新初始化事件...');
      window.dispatchEvent(new CustomEvent('scene-objects-changed'));
    }, 200);

    // 检查结果
    setTimeout(() => {
      this.completeTest();
    }, 1000);
  }

  // 获取当前系统状态
  getSystemStatus() {
    const status = {
      timestamp: new Date().toISOString(),
      objectSelection: null,
      deviceInfoManager: null,
      sceneManager: null,
      modelLoaderManager: null,
    };

    try {
      // 检查 ObjectSelection
      if (window.ObjectSelection && window.ObjectSelection.getInstance) {
        const objectSelection = window.ObjectSelection.getInstance();
        status.objectSelection = {
          exists: true,
          isUpdatingObjects: objectSelection.isUpdatingObjects || false,
          interactableObjectsCount: objectSelection.interactableObjects?.length || 0,
        };
      }

      // 检查 DeviceInfoManager
      if (window.DeviceInfoManager && window.DeviceInfoManager.getInstance) {
        const deviceInfoManager = window.DeviceInfoManager.getInstance();
        status.deviceInfoManager = {
          exists: true,
          visible: deviceInfoManager.visible || false,
        };
      }

      // 检查 SceneManager
      if (window.SceneManager && window.SceneManager.getInstance) {
        const sceneManager = window.SceneManager.getInstance();
        status.sceneManager = {
          exists: true,
          hasScene: !!sceneManager.scene,
        };
      }

      // 检查 ModelLoaderManager
      if (window.ModelLoaderManager && window.ModelLoaderManager.getInstance) {
        const modelLoaderManager = window.ModelLoaderManager.getInstance();
        status.modelLoaderManager = {
          exists: true,
          interactableObjectsCount: modelLoaderManager.interactableObjects?.length || 0,
        };
      }
    } catch (error) {
      console.error('[RecursionFixTester] 获取系统状态时出错:', error);
    }

    console.log('[RecursionFixTester] 系统状态:', status);
    return status;
  }

  // 手动触发强制修复
  forceFixRecursion() {
    console.log('[RecursionFixTester] 执行强制修复...');

    try {
      // 获取 ObjectSelection 实例
      if (window.ObjectSelection && window.ObjectSelection.getInstance) {
        const objectSelection = window.ObjectSelection.getInstance();

        // 重置更新标志
        if (objectSelection.isUpdatingObjects) {
          objectSelection.isUpdatingObjects = false;
          console.log('[RecursionFixTester] 已重置 ObjectSelection 更新标志');
        }

        // 强制重新初始化
        if (objectSelection.reinitializeEventListeners) {
          objectSelection.reinitializeEventListeners();
          console.log('[RecursionFixTester] 已重新初始化事件监听器');
        }
      }

      console.log('[RecursionFixTester] 强制修复完成');
    } catch (error) {
      console.error('[RecursionFixTester] 强制修复失败:', error);
    }
  }
}

// 自动初始化测试器
if (typeof window !== 'undefined') {
  new RecursionFixTester();
}
