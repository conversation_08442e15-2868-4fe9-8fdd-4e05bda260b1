import { defHttp } from '/@/utils/http/axios';

enum Api {
  GetFloorWeight = '/assetDevice/getFloorWeight',
  GetRoomWeight = '/assetDevice/getRoomWeight',
}

export interface FloorWeightData {
  floors: number;
  weight: number;
}

export interface RoomWeightData {
  roomName: string;
  weight: number;
}

/**
 * @description: 获取每层的重量
 */
export function getFloorWeight() {
  return defHttp.get<FloorWeightData[]>({
    url: Api.GetFloorWeight,
  });
}

/**
 * @description: 获取每个房间的重量
 */
export function getRoomWeight() {
  return defHttp.get<RoomWeightData[]>({
    url: Api.GetRoomWeight,
  });
}
