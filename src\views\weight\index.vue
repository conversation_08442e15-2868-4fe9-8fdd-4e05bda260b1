<template>
  <div class="p-4">
    <a-card :bordered="false" style="height: 100%">
      <a-tabs v-model:activeKey="activeKey" animated>
        <!-- 楼层重量 -->
        <a-tab-pane key="floor" tab="楼层重量统计">
          <FloorWeightChart />
        </a-tab-pane>

        <!-- 房间重量 -->
        <a-tab-pane key="room" tab="房间重量统计">
          <RoomWeightChart />
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import FloorWeightChart from './components/FloorWeightChart.vue';
  import RoomWeightChart from './components/RoomWeightChart.vue';

  const activeKey = ref('floor');
</script>

<style scoped>
  .ant-empty {
    margin: 50px 0;
  }
</style>
