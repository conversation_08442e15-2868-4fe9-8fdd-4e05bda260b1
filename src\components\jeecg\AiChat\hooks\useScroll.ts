import type { Ref } from 'vue';
import { nextTick, ref } from 'vue';

type ScrollElement = HTMLDivElement | null;

interface ScrollReturn {
  scrollRef: Ref<ScrollElement>;
  scrollToBottom: () => Promise<void>;
  scrollToBottomIfAtBottom: () => Promise<void>;
  scrollToTop: () => Promise<void>;
}

export function useScroll(): ScrollReturn {
  const scrollRef = ref<ScrollElement>(null);

  const scrollToBottom = async () => {
    await nextTick();
    if (scrollRef.value) scrollRef.value.scrollTop = scrollRef.value.scrollHeight;
  };

  const scrollToTop = async () => {
    await nextTick();
    if (scrollRef.value) scrollRef.value.scrollTop = 0;
  };

  const scrollToBottomIfAtBottom = async () => {
    await nextTick();
    if (scrollRef.value) {
      const { scrollTop, scrollHeight, clientHeight } = scrollRef.value;
      if (scrollTop + clientHeight >= scrollHeight - 10) {
        scrollRef.value.scrollTop = scrollRef.value.scrollHeight;
      }
    }
  };

  return {
    scrollRef,
    scrollToBottom,
    scrollToBottomIfAtBottom,
    scrollToTop,
  };
}
