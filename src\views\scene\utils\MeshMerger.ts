/**
 * MeshMerger.ts
 * 网格合并工具类，用于优化场景性能
 *
 * 功能：
 * - 合并不需要交互的mesh以减少drawcall
 * - 保留需要交互的设备mesh
 * - 按材质类型分组合并
 * - 支持内存清理和资源释放
 */
import * as THREE from 'three';
import * as BufferGeometryUtils from 'three/examples/jsm/utils/BufferGeometryUtils.js';
import { isFloorDevice } from './deviceIdentifier';
import { throttle } from 'lodash-es';

/**
 * 合并配置选项
 */
interface MergeOptions {
  /** 是否启用合并功能 */
  enabled: boolean;
  /** 最大合并面数，超过此数量将分批合并 */
  maxTriangles: number;
  /** 是否合并不同材质的mesh */
  mergeDifferentMaterials: boolean;
  /** 是否在开发环境输出详细日志 */
  verbose: boolean;
  /** 是否启用LOD（细节层次）优化 */
  enableLOD: boolean;
  /** LOD距离设置 */
  lodDistances: number[];
  /** 是否启用几何体压缩 */
  enableGeometryCompression: boolean;
  /** 是否启用视锥剔除优化 */
  enableFrustumCulling: boolean;
  /** 最小合并距离（相近的mesh优先合并） */
  minMergeDistance: number;
  /** 是否启用实例化渲染 */
  enableInstancing: boolean;
}

/**
 * 材质组信息
 */
interface MaterialGroup {
  material: THREE.Material;
  geometries: THREE.BufferGeometry[];
  originalMeshes: THREE.Mesh[];
  transforms: THREE.Matrix4[];
}

/**
 * 合并结果
 */
interface MergeResult {
  mergedMeshes: THREE.Mesh[];
  originalMeshCount: number;
  mergedMeshCount: number;
  trianglesSaved: number;
  memoryEstimate: string;
}

/**
 * LOD信息
 */
interface LODInfo {
  distance: number;
  triangleReduction: number;
  material?: THREE.Material;
}

/**
 * 网格合并器
 */
export class MeshMerger {
  private static instance: MeshMerger | null = null;

  private mergedMeshes: Map<string, THREE.Mesh[]> = new Map();
  private originalMeshBackup: Map<string, THREE.Mesh[]> = new Map();
  private isProcessing: boolean = false;
  private logCount: number = 0;
  // 新增：预处理缓存
  private preprocessedMeshes = new Map<
    string,
    {
      mergedMeshes: THREE.Mesh[];
      originalMeshes: THREE.Mesh[];
      timestamp: number;
    }
  >();

  private readonly defaultOptions: MergeOptions = {
    enabled: true,
    maxTriangles: 50000, // 每个合并mesh的最大三角面数
    mergeDifferentMaterials: false, // 默认不合并不同材质
    verbose: false, // 关闭详细日志
    enableLOD: true, // 启用LOD优化
    lodDistances: [50, 150, 300], // LOD距离阈值
    enableGeometryCompression: true, // 启用几何体压缩
    enableFrustumCulling: true, // 启用视锥剔除
    minMergeDistance: 10, // 10米内的mesh优先合并
    enableInstancing: true, // 启用实例化渲染
  };

  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): MeshMerger {
    if (!MeshMerger.instance) {
      MeshMerger.instance = new MeshMerger();
    }
    return MeshMerger.instance;
  }
  /**
   * 合并场景中的非设备mesh
   * @param scene 要处理的场景或模型
   * @param options 合并选项
   * @returns 合并结果
   */
  public mergeMeshes(scene: THREE.Object3D, options: Partial<MergeOptions> = {}): MergeResult {
    const mergeOptions = { ...this.defaultOptions, ...options };

    if (!mergeOptions.enabled || this.isProcessing) {
      return this.createEmptyResult();
    }

    this.isProcessing = true;
    console.time('meshMerging');

    try {
      console.log('[MeshMerger] 开始分析场景中的mesh...');

      // 1. 收集并分类mesh
      const { deviceMeshes, nonDeviceMeshes } = this.categorizeMeshes(scene);
      console.log(`[MeshMerger] 发现 ${deviceMeshes.length} 个设备mesh，${nonDeviceMeshes.length} 个非设备mesh`);

      if (nonDeviceMeshes.length < 2) {
        console.log('[MeshMerger] 非设备mesh数量不足，跳过合并');
        return this.createEmptyResult();
      }

      // 2. 按材质分组并优化合并顺序
      const materialGroups = this.groupMeshesByMaterial(nonDeviceMeshes, mergeOptions);
      console.log(`[MeshMerger] 按材质分为 ${materialGroups.length} 个组`);

      // 3. 优化：先处理较大的组，可能带来更好的性能收益
      materialGroups.sort((a, b) => b.geometries.length - a.geometries.length);

      // 4. 执行合并
      const { mergedMeshes, processedMeshes } = this.performMerging(materialGroups, scene, mergeOptions);

      // 5. 移除已合并的原始mesh
      this.removeOriginalMeshes(processedMeshes, scene);

      // 6. 优化：强制更新场景
      scene.updateMatrixWorld(true);

      console.timeEnd('meshMerging');

      const result = this.createMergeResult(processedMeshes, mergedMeshes, 0);
      console.log(`[MeshMerger] 合并完成: ${result.originalMeshCount} -> ${result.mergedMeshCount} mesh`);

      // 7. 触发场景更新事件
      window.dispatchEvent(
        new CustomEvent('scene-meshes-merged', {
          detail: { mergedCount: mergedMeshes.length },
        })
      );

      return result;
    } catch (error) {
      console.error('[MeshMerger] 合并过程中发生错误:', error);
      return this.createEmptyResult();
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * 分类mesh：设备mesh vs 非设备mesh
   */
  private categorizeMeshes(scene: THREE.Object3D): { deviceMeshes: THREE.Mesh[]; nonDeviceMeshes: THREE.Mesh[] } {
    const deviceMeshes: THREE.Mesh[] = [];
    const nonDeviceMeshes: THREE.Mesh[] = [];

    scene.traverse((object: THREE.Object3D) => {
      if (!(object instanceof THREE.Mesh)) return;

      const mesh = object as THREE.Mesh;

      // 跳过不适合合并的mesh
      if (this.shouldSkipMesh(mesh)) return;

      // 优先检查是否为灯条 - 所有包含 "Dengtiao" 的 mesh 都视为设备
      const isDengtiao = this.isDengtiaoMesh(mesh);

      // 检查是否是4L模型 - 4L开头的模型不应该被识别为设备
      const is4LModel = mesh.name.startsWith('4L') || (mesh.parent && mesh.parent.name.startsWith('4L'));

      // 使用deviceIdentifier中的逻辑判断是否为设备，但排除4L模型
      const isDevice = !is4LModel && (isDengtiao || isFloorDevice(mesh.name) || (mesh.parent && isFloorDevice(mesh.parent.name)));

      // 调试日志：记录灯条的分类情况（仅在详细模式下）
      if (isDengtiao && this.defaultOptions.verbose && this.logCount < 3) {
        console.log(`[MeshMerger] 检测到灯条mesh: ${mesh.name}`);
        this.logCount++;
      }

      if (isDevice) {
        deviceMeshes.push(mesh);
      } else {
        nonDeviceMeshes.push(mesh);
      }
    });

    return { deviceMeshes, nonDeviceMeshes };
  }

  /**
   * 判断mesh是否为灯条
   * 检查mesh名称或父对象名称是否包含 "Dengtiao"
   */
  private isDengtiaoMesh(mesh: THREE.Mesh): boolean {
    // 检查mesh自身名称
    if (mesh.name && mesh.name.toLowerCase().includes('dengtiao')) {
      return true;
    }

    // 检查父对象名称
    if (mesh.parent && mesh.parent.name && mesh.parent.name.toLowerCase().includes('dengtiao')) {
      return true;
    }

    // 检查祖父对象名称（可能有多层嵌套）
    let parent = mesh.parent;
    let depth = 0;
    while (parent && depth < 5) {
      // 限制检查深度，避免无限循环
      if (parent.name && parent.name.toLowerCase().includes('dengtiao')) {
        return true;
      }
      parent = parent.parent;
      depth++;
    }

    return false;
  }

  /**
   * 判断是否应该跳过某个mesh
   */
  private shouldSkipMesh(mesh: THREE.Mesh): boolean {
    // 跳过没有几何体的mesh
    if (!mesh.geometry) return true;

    // 跳过已经被标记为不可合并的mesh
    if (mesh.userData.noMerge === true) return true;

    // 跳过骨骼动画mesh
    if (mesh instanceof THREE.SkinnedMesh) return true;

    // 跳过4L开头的模型，避免合并问题
    const is4LModel = mesh.name.startsWith('4L') || (mesh.parent && mesh.parent.name.startsWith('4L'));
    if (is4LModel) {
      return true;
    }

    // 跳过透明度不为1的mesh（可能是特效）- 但是要更宽松一些
    if (mesh.material && Array.isArray(mesh.material)) {
      const shouldSkip = mesh.material.some((mat) => mat.transparent && mat.opacity < 0.8);
      return shouldSkip;
    } else if (mesh.material && (mesh.material as THREE.Material).transparent) {
      const shouldSkip = (mesh.material as THREE.Material).opacity < 0.8;
      return shouldSkip;
    }
    return false;
  }

  /**
   * 按材质类型分组mesh
   */
  private groupMeshesByMaterial(meshes: THREE.Mesh[], options: MergeOptions): MaterialGroup[] {
    const materialGroups = new Map<string, MaterialGroup>();

    for (const mesh of meshes) {
      if (!mesh.geometry) continue;

      // 确保mesh的变换矩阵是最新的
      mesh.updateMatrixWorld(true);

      // 处理多材质情况
      const materials = Array.isArray(mesh.material) ? mesh.material : [mesh.material];

      for (let i = 0; i < materials.length; i++) {
        const material = materials[i] as THREE.Material;

        // 为材质生成唯一键
        const materialKey = options.mergeDifferentMaterials ? 'merged' : this.getMaterialKey(material);

        if (!materialGroups.has(materialKey)) {
          materialGroups.set(materialKey, {
            material: material, // 不克隆，直接使用原材质
            geometries: [],
            originalMeshes: [],
            transforms: [],
          });
        }

        const group = materialGroups.get(materialKey)!;

        // 克隆几何体并正确应用变换
        const geometry = mesh.geometry.clone();

        // 检查是否是4楼模型（4L开头的模型）
        const is4LModel = mesh.name.startsWith('4L') || (mesh.parent && mesh.parent.name.startsWith('4L'));

        if (is4LModel) {
          // 4L模型使用特殊的变换处理
          const hasValidTransform = this.validateMeshTransform(mesh);

          if (hasValidTransform) {
            // 如果变换正常，使用标准的世界变换
            mesh.updateMatrixWorld(true);
            geometry.applyMatrix4(mesh.matrixWorld);
          } else {
            // 如果变换异常，只应用位置偏移，保持原始几何体
            const position = new THREE.Vector3();
            mesh.getWorldPosition(position);
            geometry.translate(position.x, position.y, position.z);
          }
        } else {
          // 其他模型使用正常的世界变换
          mesh.updateMatrixWorld(true);
          const worldMatrix = mesh.matrixWorld.clone();
          geometry.applyMatrix4(worldMatrix);
        }

        // 简化日志输出，只在详细模式下记录
        if (options.verbose && this.logCount < 2) {
          console.log(`[MeshMerger] 处理mesh: ${mesh.name}`);
          this.logCount++;
        }

        group.geometries.push(geometry);
        group.originalMeshes.push(mesh);
        group.transforms.push(mesh.matrix.clone());
      }
    }

    return Array.from(materialGroups.values());
  }

  /**
   * 生成材质的唯一键
   */
  private getMaterialKey(material: THREE.Material): string {
    const type = material.type;
    const props: string[] = [type];

    if (material instanceof THREE.MeshStandardMaterial) {
      props.push(material.color.getHexString(), material.roughness.toString(), material.metalness.toString());
      if (material.map) props.push(material.map.uuid);
    } else if (material instanceof THREE.MeshBasicMaterial) {
      props.push(material.color.getHexString());
      if (material.map) props.push(material.map.uuid);
    }
    return props.join('_');
  }

  /**
   * 执行实际的几何体合并
   */
  private performMerging(
    materialGroups: MaterialGroup[],
    scene: THREE.Object3D,
    options: MergeOptions
  ): { mergedMeshes: THREE.Mesh[]; processedMeshes: THREE.Mesh[] } {
    const mergedMeshes: THREE.Mesh[] = [];
    const processedMeshes: THREE.Mesh[] = []; // 记录被成功合并的原始mesh

    for (const group of materialGroups) {
      if (group.geometries.length < 2) {
        // 只有一个几何体，不需要合并，不将其标记为已处理
        if (options.verbose && group.originalMeshes.length > 0) {
          console.log(`[MeshMerger] 保留单独mesh: ${group.originalMeshes[0].name}`);
        }
        continue;
      }

      try {
        if (options.verbose) {
          console.log(`[MeshMerger] 合并材质组: ${group.material.type}, ${group.geometries.length} 个几何体`);
        } // 按三角面数分批合并，避免单个mesh过大
        const batches = this.batchGeometriesByTriangleCount(group.geometries, options.maxTriangles);
        const batchOriginalMeshes = this.getBatchOriginalMeshes(group.originalMeshes, group.geometries, batches);

        for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
          const batch = batches[batchIndex];
          const batchMeshes = batchOriginalMeshes[batchIndex] || [];

          if (batch.length === 0) continue;

          // 检查几何体属性兼容性
          const compatibleGeometries = this.filterCompatibleGeometries(batch, options);
          if (compatibleGeometries.length < 2) {
            if (options.verbose) {
              console.log(`[MeshMerger] 材质组 ${group.material.type} 中兼容几何体数量不足，跳过合并`);
            }
            // 不将这些mesh添加到processedMeshes，保持它们在场景中
            continue;
          }

          // 使用BufferGeometryUtils合并几何体
          const mergedGeometry = BufferGeometryUtils.mergeGeometries(compatibleGeometries, false);

          if (mergedGeometry) {
            // 优化合并后的几何体
            mergedGeometry.computeBoundingSphere();
            mergedGeometry.computeBoundingBox();

            // 创建合并后的mesh
            const mergedMesh = new THREE.Mesh(mergedGeometry, group.material.clone()); // 克隆材质，避免共享问题
            mergedMesh.name = `merged_${group.material.type}_batch_${batchIndex}`;
            mergedMesh.userData.isMerged = true;
            mergedMesh.userData.originalCount = batch.length;

            // 设置合并mesh的位置为原点，因为几何体已经包含了正确的位置信息
            mergedMesh.position.set(0, 0, 0);
            mergedMesh.rotation.set(0, 0, 0);
            mergedMesh.scale.set(1, 1, 1);
            mergedMesh.updateMatrix();

            // 确保材质设置正确
            if (group.material.transparent) {
              mergedMesh.material.transparent = true;
              mergedMesh.material.opacity = group.material.opacity;
            }

            // 添加到场景
            scene.add(mergedMesh);
            mergedMeshes.push(mergedMesh);

            // 只有成功合并的几何体对应的原始mesh才添加到processedMeshes
            const successfullyMergedCount = Math.min(compatibleGeometries.length, batchMeshes.length);
            for (let i = 0; i < successfullyMergedCount; i++) {
              if (batchMeshes[i]) {
                processedMeshes.push(batchMeshes[i]);
              }
            }

            // 日志：记录合并后mesh的信息
            if (options.verbose) {
              console.log(`[MeshMerger] 创建合并mesh: ${mergedMesh.name}, 包含 ${successfullyMergedCount} 个几何体`);
            }
          }

          // 清理批次中的几何体
          batch.forEach((geometry) => geometry.dispose());
        }
      } catch (error) {
        console.warn('[MeshMerger] 合并材质组时出错:', error);
        // 清理几何体
        group.geometries.forEach((geometry) => geometry.dispose());
      }
    }

    return { mergedMeshes, processedMeshes };
  }
  /**
   * 按三角面数对几何体分批
   */
  private batchGeometriesByTriangleCount(geometries: THREE.BufferGeometry[], maxTriangles: number): THREE.BufferGeometry[][] {
    const batches: THREE.BufferGeometry[][] = [];
    let currentBatch: THREE.BufferGeometry[] = [];
    let currentTriangleCount = 0;

    for (const geometry of geometries) {
      const triangleCount = this.getTriangleCount(geometry);

      // 如果添加这个几何体会超过最大三角面数，开始新批次
      if (currentTriangleCount + triangleCount > maxTriangles && currentBatch.length > 0) {
        batches.push(currentBatch);
        currentBatch = [];
        currentTriangleCount = 0;
      }

      currentBatch.push(geometry);
      currentTriangleCount += triangleCount;
    }

    // 添加最后一个批次
    if (currentBatch.length > 0) {
      batches.push(currentBatch);
    }

    return batches;
  } /**
   * 获取每个批次对应的原始mesh
   * 修复：正确地建立几何体和原始mesh之间的映射关系
   */
  private getBatchOriginalMeshes(
    originalMeshes: THREE.Mesh[],
    originalGeometries: THREE.BufferGeometry[],
    batches: THREE.BufferGeometry[][]
  ): THREE.Mesh[][] {
    const batchMeshes: THREE.Mesh[][] = [];

    // 创建几何体到mesh的映射
    const geometryToMeshMap = new Map<THREE.BufferGeometry, THREE.Mesh>();
    for (let i = 0; i < originalGeometries.length && i < originalMeshes.length; i++) {
      geometryToMeshMap.set(originalGeometries[i], originalMeshes[i]);
    }

    // 为每个批次找到对应的原始mesh
    for (const batch of batches) {
      const meshBatch: THREE.Mesh[] = [];
      for (const geometry of batch) {
        const mesh = geometryToMeshMap.get(geometry);
        if (mesh) {
          meshBatch.push(mesh);
        }
      }
      batchMeshes.push(meshBatch);
    }

    return batchMeshes;
  }

  /**
   * 获取几何体的三角面数
   */
  private getTriangleCount(geometry: THREE.BufferGeometry): number {
    const position = geometry.attributes.position;
    if (!position) return 0;

    if (geometry.index) {
      return geometry.index.count / 3;
    } else {
      return position.count / 3;
    }
  } /**
   * 从场景中移除已合并的原始mesh
   */
  private removeOriginalMeshes(meshes: THREE.Mesh[], scene: THREE.Object3D): void {
    // 简化逻辑：移除所有提供的mesh，但要谨慎处理
    for (const mesh of meshes) {
      try {
        // 从父对象中移除
        if (mesh.parent) {
          mesh.parent.remove(mesh);
        } else {
          scene.remove(mesh);
        }
      } catch (error) {
        console.warn('[MeshMerger] 移除原始mesh时出错:', error);
      }
    }
  }

  /**
   * 创建合并结果对象
   */
  private createMergeResult(originalMeshes: THREE.Mesh[], mergedMeshes: THREE.Mesh[], _processTime: number): MergeResult {
    const originalTriangles = originalMeshes.reduce((sum, mesh) => sum + this.getTriangleCount(mesh.geometry), 0);
    const mergedTriangles = mergedMeshes.reduce((sum, mesh) => sum + this.getTriangleCount(mesh.geometry), 0);

    // 简单的内存估算 (基于drawcall减少)
    const drawcallReduction = originalMeshes.length - mergedMeshes.length;
    const memoryEstimate = `${(drawcallReduction * 0.5).toFixed(1)}KB (drawcall优化)`;

    return {
      mergedMeshes,
      originalMeshCount: originalMeshes.length,
      mergedMeshCount: mergedMeshes.length,
      trianglesSaved: Math.max(0, originalTriangles - mergedTriangles),
      memoryEstimate,
    };
  }

  /**
   * 创建空结果
   */
  private createEmptyResult(): MergeResult {
    return {
      mergedMeshes: [],
      originalMeshCount: 0,
      mergedMeshCount: 0,
      trianglesSaved: 0,
      memoryEstimate: '0KB',
    };
  }

  /**
   * 恢复场景的原始mesh（用于调试或需要时）
   */
  public restoreOriginalMeshes(scene: THREE.Object3D): boolean {
    const sceneId = scene.uuid;
    const originalMeshes = this.originalMeshBackup.get(sceneId);
    const mergedMeshes = this.mergedMeshes.get(sceneId);

    if (!originalMeshes || !mergedMeshes) {
      return false;
    }

    console.log(`[MeshMerger] 恢复场景 ${sceneId} 的原始mesh，共 ${originalMeshes.length} 个`);

    // 移除合并的mesh
    for (const mergedMesh of mergedMeshes) {
      if (mergedMesh.parent) {
        mergedMesh.parent.remove(mergedMesh);
      } else {
        scene.remove(mergedMesh);
      }
      if (mergedMesh.geometry) mergedMesh.geometry.dispose();
      if (mergedMesh.material) {
        if (Array.isArray(mergedMesh.material)) {
          mergedMesh.material.forEach((mat) => mat.dispose());
        } else {
          mergedMesh.material.dispose();
        }
      }
    }

    // 恢复原始mesh
    for (const originalMesh of originalMeshes) {
      // 确保原始mesh有正确的父级关系
      if (!originalMesh.parent) {
        scene.add(originalMesh);
      }
    }

    // 只清理合并mesh记录，保留备份数据以便下次使用
    this.mergedMeshes.delete(sceneId);

    return true;
  }

  /**
   * 检查是否有预处理的合并结果
   */
  public hasPreprocessedMerge(sceneId: string): boolean {
    return this.originalMeshBackup.has(sceneId) && this.mergedMeshes.has(sceneId);
  }

  /**
   * 应用预处理的合并结果到场景
   */
  public applyPreprocessedMerge(scene: THREE.Object3D): boolean {
    const sceneId = scene.uuid;
    const originalMeshes = this.originalMeshBackup.get(sceneId);
    const mergedMeshes = this.mergedMeshes.get(sceneId);

    if (!originalMeshes || !mergedMeshes) {
      return false;
    }

    console.log(`[MeshMerger] 应用预处理的合并结果到场景 ${sceneId}`);

    // 隐藏原始mesh
    for (const originalMesh of originalMeshes) {
      originalMesh.visible = false;
    }

    // 添加合并的mesh到场景
    for (const mergedMesh of mergedMeshes) {
      scene.add(mergedMesh);
      mergedMesh.visible = true;
    }

    return true;
  }

  /**
   * 应用预处理的合并结果到克隆的场景
   */
  public applyPreprocessedMergeToClone(clonedScene: THREE.Object3D, originalSceneId: string): boolean {
    const originalMeshes = this.originalMeshBackup.get(originalSceneId);
    const mergedMeshes = this.mergedMeshes.get(originalSceneId);

    if (!originalMeshes || !mergedMeshes) {
      return false;
    }

    console.log(`[MeshMerger] 应用预处理的合并结果到克隆场景，原始ID: ${originalSceneId}`);

    // 找到克隆场景中对应的原始mesh并隐藏它们
    const clonedMeshesToHide: THREE.Mesh[] = [];
    clonedScene.traverse((child: THREE.Object3D) => {
      if (child instanceof THREE.Mesh) {
        // 检查是否是非设备mesh（需要被合并的mesh）
        // 使用与mergeMeshes相同的逻辑判断设备
        const isDengtiao = this.isDengtiaoMesh(child);
        const is4LModel = child.name.startsWith('4L') || (child.parent && child.parent.name.startsWith('4L'));
        const isDevice = !is4LModel && (isDengtiao || isFloorDevice(child.name) || (child.parent && isFloorDevice(child.parent.name)));

        if (!isDevice) {
          clonedMeshesToHide.push(child);
        }
      }
    });

    // 隐藏原始mesh
    for (const mesh of clonedMeshesToHide) {
      mesh.visible = false;
    }

    // 克隆并添加合并的mesh到场景
    for (const mergedMesh of mergedMeshes) {
      const clonedMergedMesh = mergedMesh.clone();
      clonedScene.add(clonedMergedMesh);
      clonedMergedMesh.visible = true;
    }

    return true;
  }

  /**
   * 从场景中移除合并mesh，但保留缓存以便重用
   */
  public clearMergedMeshes(scene: THREE.Object3D): void {
    const sceneId = scene.uuid;
    const mergedMeshes = this.mergedMeshes.get(sceneId);

    if (mergedMeshes) {
      console.log(`[MeshMerger] 从场景中移除合并mesh，保留缓存以便重用`);

      // 只从场景中移除，不销毁资源
      for (const mergedMesh of mergedMeshes) {
        if (mergedMesh.parent) {
          mergedMesh.parent.remove(mergedMesh);
        }
      }

      // 保留mergedMeshes和备份数据，以便下次重用
    }
  }

  /**
   * 清理指定场景的合并数据
   */
  public dispose(scene: THREE.Object3D): void {
    const sceneId = scene.uuid;
    const mergedMeshes = this.mergedMeshes.get(sceneId);
    const originalMeshes = this.originalMeshBackup.get(sceneId);

    console.log(`[MeshMerger] 清理场景 ${sceneId} 的合并数据`);

    if (mergedMeshes) {
      // 清理合并的mesh
      for (const mergedMesh of mergedMeshes) {
        // 从场景中移除
        if (mergedMesh.parent) {
          mergedMesh.parent.remove(mergedMesh);
        } else {
          scene.remove(mergedMesh);
        }

        // 清理几何体
        if (mergedMesh.geometry) {
          mergedMesh.geometry.dispose();
        }

        // 清理材质和纹理
        if (mergedMesh.material) {
          if (Array.isArray(mergedMesh.material)) {
            mergedMesh.material.forEach((mat) => {
              this._disposeMaterial(mat);
            });
          } else {
            this._disposeMaterial(mergedMesh.material);
          }
        }
      }
    }

    // 恢复原始mesh的可见性（如果它们被隐藏了）
    if (originalMeshes) {
      for (const originalMesh of originalMeshes) {
        if (originalMesh.parent) {
          originalMesh.visible = true;
        }
      }
    }

    // 清理备份数据
    this.mergedMeshes.delete(sceneId);
    this.originalMeshBackup.delete(sceneId);

    // 重置计数器
    this.logCount = 0;

    console.log(`[MeshMerger] 场景 ${sceneId} 的合并数据清理完成`);
  }

  /**
   * 彻底清理材质和相关纹理
   */
  private _disposeMaterial(material: THREE.Material): void {
    try {
      // 清理常见的纹理属性
      const textureProps = ['map', 'normalMap', 'roughnessMap', 'metalnessMap', 'emissiveMap', 'aoMap', 'bumpMap', 'displacementMap'];

      for (const prop of textureProps) {
        const texture = (material as any)[prop];
        if (texture && typeof texture.dispose === 'function') {
          texture.dispose();
        }
      }

      // 清理材质本身
      material.dispose();
    } catch (error) {
      console.warn('[MeshMerger] 清理材质时出错:', error);
    }
  }

  /**
   * 获取合并统计信息
   */
  public getStats(): { scenesWithMerged: number; totalMergedMeshes: number } {
    let totalMergedMeshes = 0;

    for (const meshes of this.mergedMeshes.values()) {
      totalMergedMeshes += meshes.length;
    }

    return {
      scenesWithMerged: this.mergedMeshes.size,
      totalMergedMeshes,
    };
  }

  /**
   * 创建节流版本的合并函数，用于实时场景更新
   */
  public createThrottledMerge = throttle(
    (scene: THREE.Object3D, options?: Partial<MergeOptions>) => {
      return this.mergeMeshes(scene, options);
    },
    1000 // 1秒内最多执行一次
  );

  /**
   * 过滤出具有兼容属性的几何体
   * 确保所有几何体具有相同的属性，否则合并会失败
   */
  private filterCompatibleGeometries(geometries: THREE.BufferGeometry[], options: MergeOptions): THREE.BufferGeometry[] {
    if (geometries.length === 0) return [];

    // 统计所有几何体的属性，找出最常见的属性组合
    const attributeCombinations = new Map<string, THREE.BufferGeometry[]>();

    for (const geometry of geometries) {
      const attributes = Object.keys(geometry.attributes).sort();
      // 确保至少包含position属性
      if (!attributes.includes('position')) continue;

      const attributeKey = attributes.join(',');
      if (!attributeCombinations.has(attributeKey)) {
        attributeCombinations.set(attributeKey, []);
      }
      attributeCombinations.get(attributeKey)!.push(geometry);
    }

    // 找出最大的兼容组
    let largestGroup: THREE.BufferGeometry[] = [];
    let largestGroupKey = '';

    for (const [key, group] of attributeCombinations) {
      if (group.length > largestGroup.length) {
        largestGroup = group;
        largestGroupKey = key;
      }
    }

    // 如果最大组只有一个几何体，尝试标准化属性
    if (largestGroup.length < 2 && geometries.length >= 2) {
      largestGroup = this.normalizeGeometryAttributes(geometries);
      if (options.verbose && largestGroup.length > 0) {
        console.log(`[MeshMerger] 通过属性标准化获得 ${largestGroup.length} 个兼容几何体`);
      }
    }

    if (options.verbose) {
      console.log(`[MeshMerger] 选择属性组合: ${largestGroupKey}, 兼容几何体: ${largestGroup.length}/${geometries.length}`);
      if (largestGroup.length < geometries.length) {
        const skippedCount = geometries.length - largestGroup.length;
        console.log(`[MeshMerger] 跳过 ${skippedCount} 个不兼容的几何体`);
      }
    }

    return largestGroup;
  }

  /**
   * 标准化几何体属性，确保它们具有相同的属性
   */
  private normalizeGeometryAttributes(geometries: THREE.BufferGeometry[]): THREE.BufferGeometry[] {
    if (geometries.length === 0) return [];

    // 找出所有几何体都有的公共属性
    const commonAttributes = new Set<string>();
    const firstGeometry = geometries[0];

    for (const attr of Object.keys(firstGeometry.attributes)) {
      const hasInAll = geometries.every((geo) => geo.attributes[attr]);
      if (hasInAll) {
        commonAttributes.add(attr);
      }
    }

    // 确保至少有position属性
    if (!commonAttributes.has('position')) {
      return [];
    }

    // 为缺少公共属性的几何体添加默认属性
    const normalizedGeometries: THREE.BufferGeometry[] = [];

    for (const geometry of geometries) {
      const normalizedGeometry = geometry.clone();

      // 添加缺少的属性
      for (const attr of commonAttributes) {
        if (!normalizedGeometry.attributes[attr]) {
          // 为缺少的属性创建默认值
          if (attr === 'normal') {
            normalizedGeometry.computeVertexNormals();
          } else if (attr === 'uv') {
            // 创建默认UV坐标
            const positionCount = normalizedGeometry.attributes.position.count;
            const uvArray = new Float32Array(positionCount * 2);
            normalizedGeometry.setAttribute('uv', new THREE.BufferAttribute(uvArray, 2));
          }
        }
      }

      normalizedGeometries.push(normalizedGeometry);
    }

    return normalizedGeometries;
  }

  /**
   * 验证mesh的变换是否正常
   */
  private validateMeshTransform(mesh: THREE.Mesh): boolean {
    try {
      // 检查变换矩阵是否包含异常值
      const matrix = mesh.matrixWorld;
      const elements = matrix.elements;

      // 检查是否有NaN或无穷大值
      for (const element of elements) {
        if (!isFinite(element)) {
          return false;
        }
      }

      // 检查缩放是否异常（过小或过大）
      const scale = new THREE.Vector3();
      matrix.decompose(new THREE.Vector3(), new THREE.Quaternion(), scale);

      if (scale.x < 0.001 || scale.y < 0.001 || scale.z < 0.001 || scale.x > 1000 || scale.y > 1000 || scale.z > 1000) {
        return false;
      }

      return true;
    } catch (error) {
      console.warn('[MeshMerger] 验证变换时出错:', error);
      return false;
    }
  }

  /**
   * 预处理网格合并，用于场景转换优化
   * @param modelPath 模型路径，用作缓存键
   * @param scene 要预处理的场景
   * @param options 合并选项
   * @returns 是否成功预处理
   */
  public preprocessMeshMerging(modelPath: string, scene: THREE.Object3D, options: Partial<MergeOptions> = {}): boolean {
    try {
      const cacheKey = `${modelPath}_${scene.uuid}`;

      // 检查是否已经预处理过
      if (this.preprocessedMeshes.has(cacheKey)) {
        console.log(`[MeshMerger] 网格合并已预处理: ${cacheKey}`);
        return true;
      }

      console.log(`[MeshMerger] 开始预处理网格合并: ${cacheKey}`);

      // 执行网格合并但不应用到场景
      const mergeOptions = { ...this.defaultOptions, ...options, verbose: false };
      const { deviceMeshes, nonDeviceMeshes } = this.categorizeMeshes(scene);

      if (nonDeviceMeshes.length < 2) {
        console.log(`[MeshMerger] 预处理跳过：非设备mesh数量不足 (${nonDeviceMeshes.length})`);
        return false;
      }

      // 按材质分组
      const materialGroups = this.groupMeshesByMaterial(nonDeviceMeshes, mergeOptions);

      // 执行合并但不添加到场景
      const { mergedMeshes } = this.performMergingForPreprocessing(materialGroups, mergeOptions);

      // 缓存预处理结果
      this.preprocessedMeshes.set(cacheKey, {
        mergedMeshes,
        originalMeshes: nonDeviceMeshes,
        timestamp: Date.now(),
      });

      console.log(`[MeshMerger] 预处理完成: ${cacheKey}, 生成 ${mergedMeshes.length} 个合并mesh`);
      return true;
    } catch (error) {
      console.error(`[MeshMerger] 预处理失败: ${error}`);
      return false;
    }
  }

  /**
   * 应用预处理的网格合并结果
   * @param modelPath 模型路径
   * @param scene 目标场景
   * @returns 是否成功应用
   */
  public applyPreprocessedMerging(modelPath: string, scene: THREE.Object3D): boolean {
    try {
      const cacheKey = `${modelPath}_${scene.uuid}`;
      const preprocessed = this.preprocessedMeshes.get(cacheKey);

      if (!preprocessed) {
        console.warn(`[MeshMerger] 未找到预处理的合并数据: ${cacheKey}`);
        return false;
      }

      console.log(`[MeshMerger] 应用预处理的网格合并: ${cacheKey}`);

      // 隐藏原始mesh
      for (const originalMesh of preprocessed.originalMeshes) {
        if (originalMesh.parent) {
          originalMesh.visible = false;
        }
      }

      // 添加合并mesh到场景
      for (const mergedMesh of preprocessed.mergedMeshes) {
        const clonedMesh = mergedMesh.clone();
        scene.add(clonedMesh);
        clonedMesh.visible = true;
      }

      // 更新缓存
      const sceneId = scene.uuid;
      this.mergedMeshes.set(sceneId, [...preprocessed.mergedMeshes]);
      this.originalMeshBackup.set(sceneId, [...preprocessed.originalMeshes]);

      console.log(`[MeshMerger] 预处理合并应用完成: ${preprocessed.mergedMeshes.length} 个合并mesh`);
      return true;
    } catch (error) {
      console.error(`[MeshMerger] 应用预处理合并失败: ${error}`);
      return false;
    }
  }

  /**
   * 执行合并但不添加到场景（用于预处理）
   */
  private performMergingForPreprocessing(materialGroups: MaterialGroup[], options: MergeOptions): { mergedMeshes: THREE.Mesh[] } {
    const mergedMeshes: THREE.Mesh[] = [];

    for (const group of materialGroups) {
      if (group.geometries.length < 2) continue;

      try {
        const batches = this.batchGeometriesByTriangleCount(group.geometries, options.maxTriangles);

        for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
          const batch = batches[batchIndex];
          if (batch.length === 0) continue;

          const compatibleGeometries = this.filterCompatibleGeometries(batch, options);
          if (compatibleGeometries.length < 2) continue;

          const mergedGeometry = BufferGeometryUtils.mergeGeometries(compatibleGeometries, false);

          if (mergedGeometry) {
            mergedGeometry.computeBoundingSphere();
            mergedGeometry.computeBoundingBox();

            const mergedMesh = new THREE.Mesh(mergedGeometry, group.material.clone());
            mergedMesh.name = `preprocessed_merged_${group.material.type}_batch_${batchIndex}`;
            mergedMesh.userData.isMerged = true;
            mergedMesh.userData.isPreprocessed = true;
            mergedMesh.userData.originalCount = batch.length;

            mergedMesh.position.set(0, 0, 0);
            mergedMesh.rotation.set(0, 0, 0);
            mergedMesh.scale.set(1, 1, 1);
            mergedMesh.updateMatrix();

            if (group.material.transparent) {
              mergedMesh.material.transparent = true;
              mergedMesh.material.opacity = group.material.opacity;
            }

            mergedMeshes.push(mergedMesh);
          }

          // 清理批次中的几何体
          batch.forEach((geometry) => geometry.dispose());
        }
      } catch (error) {
        console.warn('[MeshMerger] 预处理合并材质组时出错:', error);
        group.geometries.forEach((geometry) => geometry.dispose());
      }
    }

    return { mergedMeshes };
  }

  /**
   * 清理预处理缓存
   * @param maxAge 最大缓存时间（毫秒），默认5分钟
   */
  public cleanupPreprocessedCache(maxAge: number = 5 * 60 * 1000): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    for (const [key, data] of this.preprocessedMeshes) {
      if (now - data.timestamp > maxAge) {
        keysToDelete.push(key);
        // 清理mesh资源
        for (const mesh of data.mergedMeshes) {
          if (mesh.geometry) mesh.geometry.dispose();
          if (mesh.material) {
            if (Array.isArray(mesh.material)) {
              mesh.material.forEach((mat) => mat.dispose());
            } else {
              mesh.material.dispose();
            }
          }
        }
      }
    }

    keysToDelete.forEach((key) => this.preprocessedMeshes.delete(key));

    if (keysToDelete.length > 0) {
      console.log(`[MeshMerger] 清理了 ${keysToDelete.length} 个过期的预处理缓存`);
    }
  }

  /**
   * 为调试目的，临时显示原始mesh和合并mesh的对比
   * 可在开发环境中调用此方法验证视觉效果
   */
  public debugVisualizeMerge(scene: THREE.Object3D, _options: Partial<MergeOptions> = {}): void {
    const sceneId = scene.uuid;
    const mergedMeshes = this.mergedMeshes.get(sceneId);
    const originalMeshes = this.originalMeshBackup.get(sceneId);

    if (!mergedMeshes || !originalMeshes) {
      console.log('[MeshMerger] 没有可用于调试的合并数据');
      return;
    }

    // 显示原始mesh，设为半透明
    for (const mesh of originalMeshes) {
      if (mesh.parent) continue; // 已在场景中的跳过

      scene.add(mesh);

      // 将原始mesh设为半透明红色，以便区分
      if (mesh.material) {
        if (Array.isArray(mesh.material)) {
          mesh.material.forEach((mat) => {
            mat.transparent = true;
            mat.opacity = 0.5;
            if ('color' in mat) {
              (mat as any).color = new THREE.Color(0xff0000);
            }
          });
        } else {
          mesh.material.transparent = true;
          mesh.material.opacity = 0.5;
          if ('color' in mesh.material) {
            (mesh.material as any).color = new THREE.Color(0xff0000);
          }
        }
      }
    }

    // 将合并mesh设为半透明蓝色
    for (const mesh of mergedMeshes) {
      if (mesh.material) {
        if (Array.isArray(mesh.material)) {
          mesh.material.forEach((mat) => {
            mat.transparent = true;
            mat.opacity = 0.5;
            if ('color' in mat) {
              (mat as any).color = new THREE.Color(0x0000ff);
            }
          });
        } else {
          mesh.material.transparent = true;
          mesh.material.opacity = 0.5;
          if ('color' in mesh.material) {
            (mesh.material as any).color = new THREE.Color(0x0000ff);
          }
        }
      }
    }

    console.log('[MeshMerger] 调试模式: 红色=原始mesh, 蓝色=合并mesh');
  }

  /**
   * 增强版mesh合并，包含多项性能优化
   * @param scene 要处理的场景或模型
   * @param options 合并选项
   * @returns 合并结果
   */
  public enhancedMergeMeshes(scene: THREE.Object3D, options: Partial<MergeOptions> = {}): MergeResult {
    const mergeOptions = { ...this.defaultOptions, ...options };

    if (!mergeOptions.enabled || this.isProcessing) {
      return this.createEmptyResult();
    }

    this.isProcessing = true;
    console.time('enhancedMeshMerging');

    try {
      console.log('[MeshMerger] 开始增强版mesh合并...');

      // 1. 收集并分类mesh
      const { deviceMeshes, nonDeviceMeshes } = this.categorizeMeshes(scene);
      console.log(`[MeshMerger] 发现 ${deviceMeshes.length} 个设备mesh，${nonDeviceMeshes.length} 个非设备mesh`);

      if (nonDeviceMeshes.length < 2) {
        console.log('[MeshMerger] 非设备mesh数量不足，跳过合并');
        return this.createEmptyResult();
      }

      // 2. 空间分组优化 - 按距离分组相近的mesh
      const spatialGroups = this.groupMeshesBySpatialProximity(nonDeviceMeshes, mergeOptions.minMergeDistance);
      console.log(`[MeshMerger] 按空间位置分为 ${spatialGroups.length} 个组`);

      // 3. 检测重复几何体并启用实例化
      if (mergeOptions.enableInstancing) {
        const instanceGroups = this.detectAndCreateInstances(nonDeviceMeshes, scene);
        console.log(`[MeshMerger] 创建了 ${instanceGroups.length} 个实例化组`);
      }

      // 4. 按材质和空间分组
      const materialGroups = this.groupMeshesByMaterialAndSpace(spatialGroups, mergeOptions);
      console.log(`[MeshMerger] 按材质和空间分为 ${materialGroups.length} 个组`);

      // 5. 优化合并顺序 - 按影响性能的因素排序
      materialGroups.sort((a, b) => this.calculateMergeScore(b) - this.calculateMergeScore(a));

      // 6. 执行增强版合并
      const { mergedMeshes, processedMeshes } = this.performEnhancedMerging(materialGroups, scene, mergeOptions);

      // 7. 设置LOD
      if (mergeOptions.enableLOD) {
        this.setupLODForMergedMeshes(mergedMeshes, mergeOptions);
      }

      // 8. 启用视锥剔除
      if (mergeOptions.enableFrustumCulling) {
        this.enableFrustumCulling(mergedMeshes);
      }

      // 9. 移除已合并的原始mesh
      this.removeOriginalMeshes(processedMeshes, scene);

      // 10. 优化场景图结构
      this.optimizeSceneGraph(scene);

      console.timeEnd('enhancedMeshMerging');

      const result = this.createMergeResult(processedMeshes, mergedMeshes, 0);
      console.log(`[MeshMerger] 增强版合并完成: ${result.originalMeshCount} -> ${result.mergedMeshCount} mesh`);
      console.log(`[MeshMerger] 预计性能提升: ${this.estimatePerformanceGain(result)}%`);

      return result;
    } catch (error) {
      console.error('[MeshMerger] 增强版合并过程中发生错误:', error);
      return this.createEmptyResult();
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * 按空间邻近性分组mesh
   */
  private groupMeshesBySpatialProximity(meshes: THREE.Mesh[], maxDistance: number): THREE.Mesh[][] {
    const groups: THREE.Mesh[][] = [];
    const processed = new Set<THREE.Mesh>();

    for (const mesh of meshes) {
      if (processed.has(mesh)) continue;

      const group = [mesh];
      processed.add(mesh);

      const meshPosition = new THREE.Vector3();
      mesh.getWorldPosition(meshPosition);

      // 查找附近的mesh
      for (const otherMesh of meshes) {
        if (processed.has(otherMesh) || otherMesh === mesh) continue;

        const otherPosition = new THREE.Vector3();
        otherMesh.getWorldPosition(otherPosition);

        if (meshPosition.distanceTo(otherPosition) <= maxDistance) {
          group.push(otherMesh);
          processed.add(otherMesh);
        }
      }

      groups.push(group);
    }

    return groups;
  }

  /**
   * 检测重复几何体并创建实例化渲染
   */
  private detectAndCreateInstances(meshes: THREE.Mesh[], scene: THREE.Object3D): THREE.InstancedMesh[] {
    const geometryGroups = new Map<string, THREE.Mesh[]>();
    const instancedMeshes: THREE.InstancedMesh[] = [];
    const processedMeshes = new Set<THREE.Mesh>();

    // 按几何体分组（同时考虑材质）
    for (const mesh of meshes) {
      if (!mesh.geometry || processedMeshes.has(mesh)) continue;

      const geometryKey = this.getGeometryHash(mesh.geometry);
      const materialKey = this.getMaterialKey(mesh.material as THREE.Material);
      const combinedKey = `${geometryKey}_${materialKey}`;

      if (!geometryGroups.has(combinedKey)) {
        geometryGroups.set(combinedKey, []);
      }
      geometryGroups.get(combinedKey)!.push(mesh);
    }

    // 为重复的几何体创建实例化渲染
    for (const [combinedKey, groupMeshes] of geometryGroups) {
      if (groupMeshes.length >= 3) {
        // 至少3个重复才值得实例化
        try {
          const referenceMesh = groupMeshes[0];

          // 确保所有mesh的变换矩阵是最新的
          for (const mesh of groupMeshes) {
            mesh.updateMatrixWorld(true);
          }

          const instancedMesh = new THREE.InstancedMesh(
            referenceMesh.geometry.clone(), // 克隆几何体避免冲突
            referenceMesh.material instanceof Array ? referenceMesh.material[0] : referenceMesh.material,
            groupMeshes.length
          );

          // 设置每个实例的变换矩阵
          for (let i = 0; i < groupMeshes.length; i++) {
            const mesh = groupMeshes[i];
            instancedMesh.setMatrixAt(i, mesh.matrixWorld);
            processedMeshes.add(mesh); // 标记为已处理
          }

          instancedMesh.instanceMatrix.needsUpdate = true;
          instancedMesh.name = `instanced_${referenceMesh.name}_${groupMeshes.length}`;
          instancedMesh.userData.isInstanced = true;
          instancedMesh.userData.originalCount = groupMeshes.length;
          instancedMesh.userData.originalMeshes = groupMeshes; // 保存原始mesh引用

          scene.add(instancedMesh);
          instancedMeshes.push(instancedMesh);

          // 隐藏原始mesh而不是删除，以便后续可以恢复
          for (const mesh of groupMeshes) {
            mesh.visible = false;
            mesh.userData.instancedBy = instancedMesh.uuid; // 记录被实例化的信息
          }

          console.log(`[MeshMerger] 创建实例化mesh: ${instancedMesh.name}, 包含 ${groupMeshes.length} 个实例`);
        } catch (error) {
          console.warn(`[MeshMerger] 创建实例化mesh失败 (${combinedKey}):`, error);
        }
      }
    }

    return instancedMeshes;
  }

  /**
   * 生成几何体哈希用于实例化检测
   */
  private getGeometryHash(geometry: THREE.BufferGeometry): string {
    const position = geometry.attributes.position;
    if (!position) return '';

    const vertexCount = position.count;
    const faceCount = geometry.index ? geometry.index.count / 3 : vertexCount / 3;

    // 简单的哈希：顶点数 + 面数 + 边界盒大小
    geometry.computeBoundingBox();
    const bbox = geometry.boundingBox!;
    const size = bbox.getSize(new THREE.Vector3());

    return `${vertexCount}_${faceCount}_${size.x.toFixed(2)}_${size.y.toFixed(2)}_${size.z.toFixed(2)}`;
  }

  /**
   * 按材质和空间分组
   */
  private groupMeshesByMaterialAndSpace(spatialGroups: THREE.Mesh[][], options: MergeOptions): MaterialGroup[] {
    const materialGroups: MaterialGroup[] = [];

    for (const spatialGroup of spatialGroups) {
      // 在每个空间组内再按材质分组
      const subGroups = this.groupMeshesByMaterial(spatialGroup, options);
      materialGroups.push(...subGroups);
    }

    return materialGroups;
  }

  /**
   * 计算合并优先级分数
   */
  private calculateMergeScore(group: MaterialGroup): number {
    let score = 0;

    // 几何体数量越多，合并收益越大
    score += group.geometries.length * 10;

    // 总三角面数适中的优先
    const totalTriangles = group.geometries.reduce((sum, geo) => sum + this.getTriangleCount(geo), 0);
    if (totalTriangles > 1000 && totalTriangles < 30000) {
      score += 50;
    }

    // 材质复杂度影响
    if ('map' in group.material && (group.material as any).map) score += 10;
    if (group.material.transparent) score -= 5; // 透明材质合并收益较小

    return score;
  }

  /**
   * 执行增强版合并
   */
  private performEnhancedMerging(
    materialGroups: MaterialGroup[],
    scene: THREE.Object3D,
    options: MergeOptions
  ): { mergedMeshes: THREE.Mesh[]; processedMeshes: THREE.Mesh[] } {
    const mergedMeshes: THREE.Mesh[] = [];
    const processedMeshes: THREE.Mesh[] = [];

    for (const group of materialGroups) {
      if (group.geometries.length < 2) continue;

      try {
        // 智能批处理 - 考虑几何体复杂度和空间位置
        const batches = this.intelligentBatching(group.geometries, options);
        const batchOriginalMeshes = this.getBatchOriginalMeshes(group.originalMeshes, group.geometries, batches);

        for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
          const batch = batches[batchIndex];
          const batchMeshes = batchOriginalMeshes[batchIndex] || [];

          if (batch.length === 0) continue;

          // 几何体预处理和压缩
          const processedGeometries = this.preprocessGeometries(batch, options);
          const compatibleGeometries = this.filterCompatibleGeometries(processedGeometries, options);

          if (compatibleGeometries.length < 2) continue;

          // 执行合并
          const mergedGeometry = BufferGeometryUtils.mergeGeometries(compatibleGeometries, false);

          if (mergedGeometry) {
            // 后处理优化
            this.optimizeGeometry(mergedGeometry, options);

            // 创建优化的合并mesh
            const mergedMesh = this.createOptimizedMergedMesh(mergedGeometry, group.material, batchIndex, batch.length);

            scene.add(mergedMesh);
            mergedMeshes.push(mergedMesh);

            // 记录成功合并的mesh
            const successfullyMergedCount = Math.min(compatibleGeometries.length, batchMeshes.length);
            for (let i = 0; i < successfullyMergedCount; i++) {
              if (batchMeshes[i]) {
                processedMeshes.push(batchMeshes[i]);
              }
            }
          }

          // 清理批次
          batch.forEach((geometry) => geometry.dispose());
        }
      } catch (error) {
        console.warn('[MeshMerger] 增强版合并材质组时出错:', error);
        group.geometries.forEach((geometry) => geometry.dispose());
      }
    }

    return { mergedMeshes, processedMeshes };
  }

  /**
   * 智能批处理算法
   */
  private intelligentBatching(geometries: THREE.BufferGeometry[], options: MergeOptions): THREE.BufferGeometry[][] {
    const batches: THREE.BufferGeometry[][] = [];

    // 按复杂度排序
    const sortedGeometries = [...geometries].sort((a, b) => this.getTriangleCount(a) - this.getTriangleCount(b));

    let currentBatch: THREE.BufferGeometry[] = [];
    let currentTriangleCount = 0;

    for (const geometry of sortedGeometries) {
      const triangleCount = this.getTriangleCount(geometry);

      // 动态调整批次大小
      const dynamicMaxTriangles = this.calculateDynamicBatchSize(currentBatch, options.maxTriangles);

      if (currentTriangleCount + triangleCount > dynamicMaxTriangles && currentBatch.length > 0) {
        batches.push(currentBatch);
        currentBatch = [];
        currentTriangleCount = 0;
      }

      currentBatch.push(geometry);
      currentTriangleCount += triangleCount;
    }

    if (currentBatch.length > 0) {
      batches.push(currentBatch);
    }

    return batches;
  }

  /**
   * 计算动态批次大小
   */
  private calculateDynamicBatchSize(currentBatch: THREE.BufferGeometry[], baseMaxTriangles: number): number {
    // 根据当前批次的几何体复杂度动态调整
    if (currentBatch.length === 0) return baseMaxTriangles;

    const avgComplexity = currentBatch.reduce((sum, geo) => sum + this.getTriangleCount(geo), 0) / currentBatch.length;

    // 简单几何体可以合并更多
    if (avgComplexity < 100) return baseMaxTriangles * 1.5;
    if (avgComplexity > 5000) return baseMaxTriangles * 0.7;

    return baseMaxTriangles;
  }

  /**
   * 几何体预处理和压缩
   */
  private preprocessGeometries(geometries: THREE.BufferGeometry[], options: MergeOptions): THREE.BufferGeometry[] {
    const processed: THREE.BufferGeometry[] = [];

    for (const geometry of geometries) {
      let processedGeometry = geometry.clone();

      if (options.enableGeometryCompression) {
        // 移除重复顶点
        processedGeometry = this.removeDuplicateVertices(processedGeometry);

        // 压缩法线精度（对于远距离物体）
        this.compressNormals(processedGeometry);

        // 优化UV坐标
        this.optimizeUVCoordinates(processedGeometry);
      }

      processed.push(processedGeometry);
    }

    return processed;
  }

  /**
   * 移除重复顶点
   */
  private removeDuplicateVertices(geometry: THREE.BufferGeometry): THREE.BufferGeometry {
    const positions = geometry.attributes.position.array;
    const normals = geometry.attributes.normal?.array;
    const uvs = geometry.attributes.uv?.array;

    const verticesMap = new Map<string, number>();
    const newPositions: number[] = [];
    const newNormals: number[] = [];
    const newUVs: number[] = [];
    const indexMapping: number[] = [];

    let newIndex = 0;

    for (let i = 0; i < positions.length; i += 3) {
      const x = positions[i];
      const y = positions[i + 1];
      const z = positions[i + 2];

      // 创建顶点键值（包含位置、法线、UV）
      let key = `${x.toFixed(4)},${y.toFixed(4)},${z.toFixed(4)}`;

      if (normals) {
        const nx = normals[i];
        const ny = normals[i + 1];
        const nz = normals[i + 2];
        key += `,${nx.toFixed(3)},${ny.toFixed(3)},${nz.toFixed(3)}`;
      }

      if (uvs) {
        const u = uvs[(i / 3) * 2];
        const v = uvs[(i / 3) * 2 + 1];
        key += `,${u.toFixed(4)},${v.toFixed(4)}`;
      }

      if (verticesMap.has(key)) {
        indexMapping.push(verticesMap.get(key)!);
      } else {
        verticesMap.set(key, newIndex);
        indexMapping.push(newIndex);

        newPositions.push(x, y, z);

        if (normals) {
          newNormals.push(normals[i], normals[i + 1], normals[i + 2]);
        }

        if (uvs) {
          newUVs.push(uvs[(i / 3) * 2], uvs[(i / 3) * 2 + 1]);
        }

        newIndex++;
      }
    }

    // 创建优化后的几何体
    const optimizedGeometry = new THREE.BufferGeometry();
    optimizedGeometry.setAttribute('position', new THREE.Float32BufferAttribute(newPositions, 3));

    if (newNormals.length > 0) {
      optimizedGeometry.setAttribute('normal', new THREE.Float32BufferAttribute(newNormals, 3));
    }

    if (newUVs.length > 0) {
      optimizedGeometry.setAttribute('uv', new THREE.Float32BufferAttribute(newUVs, 2));
    }

    // 重建索引
    if (geometry.index) {
      const oldIndices = geometry.index.array;
      const newIndices: number[] = [];

      for (let i = 0; i < oldIndices.length; i++) {
        newIndices.push(indexMapping[oldIndices[i]]);
      }

      optimizedGeometry.setIndex(newIndices);
    } else {
      optimizedGeometry.setIndex(indexMapping);
    }

    return optimizedGeometry;
  }

  /**
   * 压缩法线精度
   */
  private compressNormals(geometry: THREE.BufferGeometry): void {
    const normalAttribute = geometry.attributes.normal;
    if (!normalAttribute) return;

    const normals = normalAttribute.array;

    // 将法线精度降低到2位小数（对于合并的远距离物体足够了）
    for (let i = 0; i < normals.length; i++) {
      normals[i] = Math.round(normals[i] * 100) / 100;
    }

    normalAttribute.needsUpdate = true;
  }

  /**
   * 优化UV坐标
   */
  private optimizeUVCoordinates(geometry: THREE.BufferGeometry): void {
    const uvAttribute = geometry.attributes.uv;
    if (!uvAttribute) return;

    const uvs = uvAttribute.array;

    // 检查UV坐标是否在合理范围内，超出部分进行模运算
    for (let i = 0; i < uvs.length; i += 2) {
      uvs[i] = uvs[i] % 1; // U坐标
      uvs[i + 1] = uvs[i + 1] % 1; // V坐标

      // 确保正值
      if (uvs[i] < 0) uvs[i] += 1;
      if (uvs[i + 1] < 0) uvs[i + 1] += 1;
    }

    uvAttribute.needsUpdate = true;
  }

  /**
   * 优化几何体
   */
  private optimizeGeometry(geometry: THREE.BufferGeometry, options: MergeOptions): void {
    // 计算边界盒和边界球
    geometry.computeBoundingBox();
    geometry.computeBoundingSphere();

    // 如果启用几何体压缩，进一步优化
    if (options.enableGeometryCompression) {
      // 合并后的几何体通常不需要太高精度的法线
      if (geometry.attributes.normal) {
        this.simplifyNormals(geometry);
      }

      // 压缩索引
      this.compressIndices(geometry);
    }
  }

  /**
   * 简化法线
   */
  private simplifyNormals(geometry: THREE.BufferGeometry): void {
    const normalAttribute = geometry.attributes.normal;
    if (!normalAttribute) return;

    // 对于合并的大型mesh，可以适当简化法线
    // 这里保持现有逻辑，实际项目中可以根据需要实现更复杂的法线简化
  }

  /**
   * 压缩索引
   */
  private compressIndices(geometry: THREE.BufferGeometry): void {
    if (!geometry.index) return;

    const indices = geometry.index.array;
    const maxIndex = Math.max(...Array.from(indices));

    // 如果索引值范围较小，使用更紧凑的数据类型
    if (maxIndex < 65536 && indices instanceof Uint32Array) {
      geometry.setIndex(new THREE.Uint16BufferAttribute(Array.from(indices), 1));
    }
  }

  /**
   * 创建优化的合并mesh
   */
  private createOptimizedMergedMesh(geometry: THREE.BufferGeometry, material: THREE.Material, batchIndex: number, originalCount: number): THREE.Mesh {
    const mergedMesh = new THREE.Mesh(geometry, material.clone());
    mergedMesh.name = `enhanced_merged_${material.type}_batch_${batchIndex}`;
    mergedMesh.userData.isMerged = true;
    mergedMesh.userData.isEnhanced = true;
    mergedMesh.userData.originalCount = originalCount;

    // 启用矩阵自动更新优化
    mergedMesh.matrixAutoUpdate = false;
    mergedMesh.updateMatrix();

    // 设置渲染优化标志
    mergedMesh.frustumCulled = true;

    // 材质优化
    this.optimizeMaterial(mergedMesh.material as THREE.Material);

    return mergedMesh;
  }

  /**
   * 优化材质
   */
  private optimizeMaterial(material: THREE.Material): void {
    // 禁用不必要的材质特性以提升性能
    if ('needsUpdate' in material) {
      (material as any).needsUpdate = false;
    }

    // 对于合并后的mesh，可以简化一些材质属性
    if (material instanceof THREE.MeshLambertMaterial || material instanceof THREE.MeshPhongMaterial) {
      // 降低材质的复杂度
      material.flatShading = true; // 平面着色通常足够
    }
  }

  /**
   * 为合并的mesh设置LOD
   */
  private setupLODForMergedMeshes(mergedMeshes: THREE.Mesh[], options: MergeOptions): void {
    for (const mesh of mergedMeshes) {
      if (this.getTriangleCount(mesh.geometry) > 10000) {
        // 只为复杂mesh设置LOD
        const lod = this.createLODForMesh(mesh, options.lodDistances);
        if (lod && mesh.parent) {
          mesh.parent.add(lod);
          mesh.parent.remove(mesh);
        }
      }
    }
  }

  /**
   * 为单个mesh创建LOD
   */
  private createLODForMesh(mesh: THREE.Mesh, distances: number[]): THREE.LOD | null {
    try {
      const lod = new THREE.LOD();

      // 添加原始细节级别
      lod.addLevel(mesh, 0);

      // 创建简化版本
      for (let i = 0; i < distances.length; i++) {
        const distance = distances[i];
        const reduction = 0.7 ** (i + 1); // 每级降低30%的复杂度

        const simplifiedMesh = this.createSimplifiedMesh(mesh, reduction);
        if (simplifiedMesh) {
          lod.addLevel(simplifiedMesh, distance);
        }
      }

      lod.name = `lod_${mesh.name}`;
      lod.userData.isLOD = true;

      return lod;
    } catch (error) {
      console.warn('[MeshMerger] 创建LOD失败:', error);
      return null;
    }
  }

  /**
   * 创建简化版mesh
   */
  private createSimplifiedMesh(originalMesh: THREE.Mesh, reduction: number): THREE.Mesh | null {
    try {
      // 简单的几何体简化：降低顶点密度
      const geometry = originalMesh.geometry.clone();

      // 这里实现一个简单的顶点减少算法
      // 实际项目中可以使用更复杂的网格简化算法
      const simplifiedGeometry = this.simplifyGeometry(geometry, reduction);

      if (simplifiedGeometry) {
        const simplifiedMesh = new THREE.Mesh(simplifiedGeometry, originalMesh.material);
        simplifiedMesh.name = `${originalMesh.name}_simplified_${reduction.toFixed(2)}`;
        simplifiedMesh.userData.isSimplified = true;
        simplifiedMesh.userData.simplificationRatio = reduction;

        return simplifiedMesh;
      }

      return null;
    } catch (error) {
      console.warn('[MeshMerger] 创建简化mesh失败:', error);
      return null;
    }
  }

  /**
   * 简化几何体
   */
  private simplifyGeometry(geometry: THREE.BufferGeometry, targetRatio: number): THREE.BufferGeometry | null {
    // 简单的顶点抽取算法
    const positions = geometry.attributes.position;
    if (!positions) return null;

    const vertexCount = positions.count;
    const targetVertexCount = Math.floor(vertexCount * targetRatio);

    if (targetVertexCount >= vertexCount || targetVertexCount < 3) {
      return geometry.clone();
    }

    // 创建简化的几何体
    const step = Math.floor(vertexCount / targetVertexCount);
    const newPositions: number[] = [];
    const newNormals: number[] = [];
    const newUVs: number[] = [];

    const normals = geometry.attributes.normal;
    const uvs = geometry.attributes.uv;

    for (let i = 0; i < vertexCount; i += step) {
      // 位置
      newPositions.push(positions.getX(i), positions.getY(i), positions.getZ(i));

      // 法线
      if (normals) {
        newNormals.push(normals.getX(i), normals.getY(i), normals.getZ(i));
      }

      // UV
      if (uvs) {
        newUVs.push(uvs.getX(i), uvs.getY(i));
      }
    }

    const simplifiedGeometry = new THREE.BufferGeometry();
    simplifiedGeometry.setAttribute('position', new THREE.Float32BufferAttribute(newPositions, 3));

    if (newNormals.length > 0) {
      simplifiedGeometry.setAttribute('normal', new THREE.Float32BufferAttribute(newNormals, 3));
    } else {
      simplifiedGeometry.computeVertexNormals();
    }

    if (newUVs.length > 0) {
      simplifiedGeometry.setAttribute('uv', new THREE.Float32BufferAttribute(newUVs, 2));
    }

    return simplifiedGeometry;
  }

  /**
   * 启用视锥剔除优化
   */
  private enableFrustumCulling(meshes: THREE.Mesh[]): void {
    for (const mesh of meshes) {
      mesh.frustumCulled = true;

      // 为大型合并mesh设置更精确的边界盒
      if (this.getTriangleCount(mesh.geometry) > 20000) {
        mesh.geometry.computeBoundingBox();
        mesh.geometry.computeBoundingSphere();

        // 可以根据需要调整边界盒的大小以优化剔除
        const bbox = mesh.geometry.boundingBox;
        if (bbox) {
          const size = bbox.getSize(new THREE.Vector3());
          // 如果边界盒过大，可能影响剔除效果
          if (size.length() > 1000) {
            console.warn(`[MeshMerger] Mesh ${mesh.name} 的边界盒较大，可能影响视锥剔除效果`);
          }
        }
      }
    }
  }

  /**
   * 优化场景图结构
   */
  private optimizeSceneGraph(scene: THREE.Object3D): void {
    // 移除空的父节点
    this.removeEmptyParents(scene);

    // 扁平化深层嵌套
    this.flattenDeepNesting(scene);

    // 重新组织场景图以优化遍历
    this.reorganizeSceneGraph(scene);
  }

  /**
   * 移除空的父节点
   */
  private removeEmptyParents(object: THREE.Object3D): void {
    const toRemove: THREE.Object3D[] = [];

    object.traverse((child) => {
      if (child.children.length === 0 && !(child instanceof THREE.Mesh) && !(child instanceof THREE.Light) && !(child instanceof THREE.Camera)) {
        toRemove.push(child);
      }
    });

    for (const empty of toRemove) {
      if (empty.parent) {
        empty.parent.remove(empty);
      }
    }
  }

  /**
   * 扁平化深层嵌套
   */
  private flattenDeepNesting(scene: THREE.Object3D, maxDepth: number = 5): void {
    const flattenedMeshes: THREE.Mesh[] = [];

    scene.traverse((object) => {
      if (object instanceof THREE.Mesh && this.getObjectDepth(object) > maxDepth) {
        flattenedMeshes.push(object);
      }
    });

    // 将深度过大的mesh提升到较浅的层级
    for (const mesh of flattenedMeshes) {
      if (mesh.parent && mesh.parent !== scene) {
        mesh.parent.remove(mesh);
        scene.add(mesh);
      }
    }
  }

  /**
   * 获取对象在场景图中的深度
   */
  private getObjectDepth(object: THREE.Object3D): number {
    let depth = 0;
    let current = object.parent;

    while (current) {
      depth++;
      current = current.parent;
    }

    return depth;
  }

  /**
   * 重新组织场景图
   */
  private reorganizeSceneGraph(scene: THREE.Object3D): void {
    // 按类型分组objects
    const meshes: THREE.Mesh[] = [];
    const lights: THREE.Light[] = [];
    const others: THREE.Object3D[] = [];

    // 收集所有直接子对象
    const children = [...scene.children];

    for (const child of children) {
      if (child instanceof THREE.Mesh) {
        meshes.push(child);
      } else if (child instanceof THREE.Light) {
        lights.push(child);
      } else {
        others.push(child);
      }
    }

    // 如果合并后的mesh数量很多，可以按空间位置进一步分组
    if (meshes.length > 50) {
      this.spatiallyGroupMeshes(scene, meshes);
    }
  }

  /**
   * 按空间位置分组mesh
   */
  private spatiallyGroupMeshes(scene: THREE.Object3D, meshes: THREE.Mesh[]): void {
    // 计算场景的边界盒
    const bbox = new THREE.Box3();
    for (const mesh of meshes) {
      mesh.geometry.computeBoundingBox();
      if (mesh.geometry.boundingBox) {
        bbox.union(mesh.geometry.boundingBox);
      }
    }

    // 将空间分割为网格
    const gridSize = 4; // 4x4x4 网格
    const size = bbox.getSize(new THREE.Vector3());
    const cellSize = new THREE.Vector3(size.x / gridSize, size.y / gridSize, size.z / gridSize);

    const spatialGroups = new Map<string, THREE.Mesh[]>();

    for (const mesh of meshes) {
      const position = new THREE.Vector3();
      mesh.getWorldPosition(position);

      // 计算网格坐标
      const gridX = Math.floor((position.x - bbox.min.x) / cellSize.x);
      const gridY = Math.floor((position.y - bbox.min.y) / cellSize.y);
      const gridZ = Math.floor((position.z - bbox.min.z) / cellSize.z);

      const key = `${gridX}_${gridY}_${gridZ}`;

      if (!spatialGroups.has(key)) {
        spatialGroups.set(key, []);
      }

      spatialGroups.get(key)!.push(mesh);
    }

    // 为每个空间组创建容器
    for (const [key, groupMeshes] of spatialGroups) {
      if (groupMeshes.length > 3) {
        // 只为包含较多mesh的组创建容器
        const container = new THREE.Group();
        container.name = `spatial_group_${key}`;

        for (const mesh of groupMeshes) {
          if (mesh.parent === scene) {
            scene.remove(mesh);
            container.add(mesh);
          }
        }

        scene.add(container);
      }
    }
  }

  /**
   * 估算性能提升
   */
  private estimatePerformanceGain(result: MergeResult): number {
    const drawcallReduction = result.originalMeshCount - result.mergedMeshCount;
    const drawcallGain = (drawcallReduction / result.originalMeshCount) * 50; // drawcall减少的性能收益

    const triangleReduction = result.trianglesSaved;
    const triangleGain = Math.min((triangleReduction / 100000) * 10, 20); // 三角面减少的收益

    const totalGain = Math.min(drawcallGain + triangleGain, 80); // 最大80%提升

    return Math.round(totalGain);
  }
}

/**
 * 导出单例实例的便捷方法
 */
export const meshMerger = MeshMerger.getInstance();
