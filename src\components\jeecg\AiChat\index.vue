<template>
  <div ref="chatContainerRef" class="relative h-full flex overflow-hidden rounded-md border shadow-md" :style="chatContainerStyle">
    <template v-if="dataSource">
      <div class="absolute left-0 h-full w-260px transition-left duration-300" :class="[expand ? 'left-0' : '-left-260px']">
        <div class="w-full h-full overflow-hidden">
          <slide v-if="uuid" :dataSource="dataSource" @save="handleSave" @update:dataSource="updateDataSource" />
        </div>
        <div
          class="absolute top-1/2 right-0 -translate-y-1/2 translate-x-1/2 w-24px h-24px rounded-full flex items-center justify-center text-18px text-gray-800 border border-gray-100 bg-white shadow-sm transition duration-300 cursor-pointer z-1"
          @click="handleToggle"
        >
          <span class="transition-transform duration-300" :class="[expand ? 'rotate-180' : 'rotate-0']">
            <svg class="w-1em h-1em align-top" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M5.64645 3.14645C5.45118 3.34171 5.45118 3.65829 5.64645 3.85355L9.79289 8L5.64645 12.1464C5.45118 12.3417 5.45118 12.6583 5.64645 12.8536C5.84171 13.0488 6.15829 13.0488 6.35355 12.8536L10.8536 8.35355C11.0488 8.15829 11.0488 7.84171 10.8536 7.64645L6.35355 3.14645C6.15829 2.95118 5.84171 2.95118 5.64645 3.14645Z"
                fill="currentColor"
              />
            </svg>
          </span>
        </div>
      </div>
      <div class="transition-[margin-left] duration-300 flex-1 min-w-0" :class="[expand ? 'ml-260px' : 'ml-0']">
        <chat
          v-if="uuid && chatVisible"
          :uuid="String(uuid)"
          :chatData="chatData"
          :dataSource="dataSource"
          :modelKey="currentModelKey"
          @save="handleSave"
          @updateTitle="handleUpdateTitle"
        />
      </div>
    </template>
    <Spin v-else :spinning="true" class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" />
  </div>
</template>

<script setup lang="ts">
  import slide from './components/AiSlide.vue';
  import chat from './components/AiChat.vue';
  import { Spin } from 'ant-design-vue';
  import { ref, watch, nextTick, onUnmounted, computed } from 'vue';
  import { useUserStore } from '/@/store/modules/user';
  import { JEECG_CHAT_KEY } from '/@/enums/cacheEnum';
  import { defaultModelId } from '/@/enums/httpEnum';

  // 更新ChatMessage接口定义
  interface ChatMessage {
    dateTime: string;
    text: string;
    inversion: boolean;
    error: boolean;
    loading: boolean;
    conversationOptions: any;
    requestOptions: any;
    thought_process?: string;
  }

  interface ChatItem {
    uuid: number;
    data: ChatMessage[];
  }

  interface HistoryItem {
    uuid: number;
    title: string;
    isEdit: boolean;
  }

  interface ChatDataSource {
    active: number;
    usingContext: boolean;
    selectedModel: string;
    history: HistoryItem[];
    chat: ChatItem[];
  }

  const userId = useUserStore().getUserInfo?.id;
  const localKey = JEECG_CHAT_KEY + userId;
  let timer: any = null;
  let unwatch01: any = null;
  let unwatch02: any = null;
  const dataSource = ref<ChatDataSource | null>(null);
  const uuid = ref<number | null>(null);
  const chatData = ref<ChatMessage[]>([]);
  const expand = ref<any>(true);
  const chatVisible = ref(true);
  const chatContainerRef = ref<any>(null);
  const chatContainerStyle = ref({});
  const handleToggle = () => {
    expand.value = !expand.value;
  };

  // 更新数据源
  const updateDataSource = (newDataSource: ChatDataSource) => {
    dataSource.value = newDataSource;

    // 如果active发生变化，更新当前chat数据
    if (dataSource.value && dataSource.value.chat) {
      const findItem = dataSource.value.chat.find((item: ChatItem) => item.uuid === dataSource.value!.active);
      if (findItem) {
        uuid.value = findItem.uuid;
        chatData.value = findItem.data.map((item: ChatMessage) => ({
          dateTime: item.dateTime || new Date().toLocaleString(),
          text: item.text || '',
          inversion: !!item.inversion,
          error: !!item.error,
          loading: !!item.loading,
          conversationOptions: item.conversationOptions || null,
          requestOptions: item.requestOptions || null,
          thought_process: item.thought_process || '',
        }));
      }
    }
  };

  // 初始化数据
  const init = () => {
    const initialData: ChatDataSource = {
      active: 1002,
      usingContext: true,
      selectedModel: defaultModelId || 'CUSTOM',
      history: [{ uuid: 1002, title: '新建聊天', isEdit: false }],
      chat: [{ uuid: 1002, data: [] }],
    };

    try {
      const savedData = localStorage.getItem(localKey);
      if (savedData) {
        const parsedData = JSON.parse(savedData);
        if (parsedData?.history?.length) {
          dataSource.value = parsedData;
        } else {
          dataSource.value = initialData;
        }
      } else {
        dataSource.value = initialData;
      }
    } catch (e) {
      console.error('初始化错误:', e);
      dataSource.value = initialData;
    }
    !unwatch01 && execute();
  };

  const handleSave = () => {
    try {
      localStorage.setItem(localKey, JSON.stringify(dataSource.value));
    } catch (e) {
      console.error('保存数据错误:', e);
    }
    setTimeout(() => {
      clearTimeout(timer);
    }, 50);
  };

  // 处理标题更新
  const handleUpdateTitle = (title: string) => {
    // 更新当前会话的标题
    if (dataSource.value && dataSource.value.history) {
      const currentChat = dataSource.value.history.find((item: HistoryItem) => item.uuid === uuid.value);
      if (currentChat) {
        currentChat.title = title;
        handleSave();
      }
    }
  };

  // 监听数据变化执行操作
  const execute = () => {
    unwatch01 = watch(
      () => dataSource.value?.active,
      (value: number | undefined) => {
        if (value) {
          const findItem = dataSource.value?.chat.find((item: ChatItem) => item.uuid === value);
          if (findItem) {
            uuid.value = findItem.uuid;
            // 创建新的ChatMessage数组
            chatData.value = findItem.data.map((item: ChatMessage) => ({
              dateTime: item.dateTime || new Date().toLocaleString(),
              text: item.text || '',
              inversion: !!item.inversion,
              error: !!item.error,
              loading: !!item.loading,
              conversationOptions: item.conversationOptions || null,
              requestOptions: item.requestOptions || null,
              thought_process: item.thought_process || '',
            }));
          }
          chatVisible.value = false;
          nextTick(() => {
            chatVisible.value = true;
          });
        }
      },
      { immediate: true }
    );
    unwatch02 = watch(
      () => dataSource.value,
      (newValue: ChatDataSource | null) => {
        if (newValue) {
          clearInterval(timer);
          timer = setTimeout(() => {
            try {
              localStorage.setItem(localKey, JSON.stringify(dataSource.value));
            } catch (e) {
              console.error('自动保存失败:', e);
            }
          }, 2e3);
        }
      },
      { deep: true }
    );
  };

  const currentModelKey = computed(() => {
    return dataSource.value?.selectedModel || defaultModelId || 'CUSTOM';
  });

  onUnmounted(() => {
    unwatch01 && unwatch01();
    unwatch02 && unwatch02();
  });

  watch(
    () => chatContainerRef.value,
    () => {
      chatContainerStyle.value = { height: `${chatContainerRef.value.offsetHeight}px` };
    }
  );

  init();
</script>
